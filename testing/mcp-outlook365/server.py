"""MCP Outlook 365 Server - Complete Microsoft Graph API integration."""

import logging
import sys
from typing import Any, Dict, List, Optional
from mcp.server.fastmcp import FastMC<PERSON>, Context
from mcp.server.fastmcp.exceptions import FastMCPError
from graph_client import Graph<PERSON>lient, Graph<PERSON><PERSON>rror
from tools.mail_tools import MailTools
from tools.calendar_tools import CalendarTools
from tools.contacts_tools import ContactsTools
from resources.outlook_resources import OutlookResources
from config import ALL_SCOPES

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Create MCP server
mcp = FastMCP("Outlook365-MCP-Server")

def get_access_token_from_context(ctx: Context) -> str:
    """Extract access token from MCP context."""
    # The access token should be passed in the request context
    # This will be provided by the frontend application
    request_context = ctx.request_context
    
    # Check for authorization header
    if hasattr(request_context, 'headers') and request_context.headers:
        auth_header = request_context.headers.get('authorization') or request_context.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            return auth_header.split(' ', 1)[1]
    
    # Check for access_token parameter
    if hasattr(request_context, 'params') and request_context.params:
        access_token = request_context.params.get('access_token')
        if access_token:
            return access_token
    
    raise FastMCPError("Access token not provided. Please include Bearer token in Authorization header.")

def create_graph_client(ctx: Context) -> GraphClient:
    """Create Graph client with access token from context."""
    access_token = get_access_token_from_context(ctx)
    return GraphClient(access_token)

# ============================================================================
# MAIL TOOLS
# ============================================================================

@mcp.tool()
async def list_messages(
    ctx: Context,
    folder_id: str = "inbox",
    select: Optional[str] = None,
    filter_query: Optional[str] = None,
    order_by: Optional[str] = None,
    top: int = 50,
    skip: int = 0
) -> Dict[str, Any]:
    """List messages in a mail folder.
    
    Args:
        folder_id: Mail folder ID or well-known name (inbox, drafts, sentitems, etc.)
        select: Comma-separated list of properties to select
        filter_query: OData filter query
        order_by: OData orderby query
        top: Number of items to return (max 999)
        skip: Number of items to skip
    """
    try:
        client = create_graph_client(ctx)
        mail_tools = MailTools(client)
        return await mail_tools.list_messages(folder_id, select, filter_query, order_by, top, skip)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in list_messages: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def get_message(ctx: Context, message_id: str, select: Optional[str] = None) -> Dict[str, Any]:
    """Get a specific message by ID.
    
    Args:
        message_id: The message ID
        select: Comma-separated list of properties to select
    """
    try:
        client = create_graph_client(ctx)
        mail_tools = MailTools(client)
        return await mail_tools.get_message(message_id, select)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in get_message: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def send_message(ctx: Context, message_data: Dict[str, Any]) -> Dict[str, Any]:
    """Send an email message.
    
    Args:
        message_data: Message data including subject, body, recipients, etc.
    """
    try:
        client = create_graph_client(ctx)
        mail_tools = MailTools(client)
        return await mail_tools.send_message(message_data)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in send_message: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def create_draft(ctx: Context, message_data: Dict[str, Any]) -> Dict[str, Any]:
    """Create a draft message.
    
    Args:
        message_data: Message data including subject, body, recipients, etc.
    """
    try:
        client = create_graph_client(ctx)
        mail_tools = MailTools(client)
        return await mail_tools.create_draft(message_data)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in create_draft: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def update_message(ctx: Context, message_id: str, update_data: Dict[str, Any]) -> Dict[str, Any]:
    """Update a message (mark as read, move, etc.).
    
    Args:
        message_id: The message ID
        update_data: Data to update
    """
    try:
        client = create_graph_client(ctx)
        mail_tools = MailTools(client)
        return await mail_tools.update_message(message_id, update_data)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in update_message: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def delete_message(ctx: Context, message_id: str) -> Dict[str, Any]:
    """Delete a message.
    
    Args:
        message_id: The message ID
    """
    try:
        client = create_graph_client(ctx)
        mail_tools = MailTools(client)
        return await mail_tools.delete_message(message_id)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in delete_message: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def move_message(ctx: Context, message_id: str, destination_folder_id: str) -> Dict[str, Any]:
    """Move a message to another folder.
    
    Args:
        message_id: The message ID
        destination_folder_id: Target folder ID
    """
    try:
        client = create_graph_client(ctx)
        mail_tools = MailTools(client)
        return await mail_tools.move_message(message_id, destination_folder_id)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in move_message: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def copy_message(ctx: Context, message_id: str, destination_folder_id: str) -> Dict[str, Any]:
    """Copy a message to another folder.
    
    Args:
        message_id: The message ID
        destination_folder_id: Target folder ID
    """
    try:
        client = create_graph_client(ctx)
        mail_tools = MailTools(client)
        return await mail_tools.copy_message(message_id, destination_folder_id)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in copy_message: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def reply_to_message(ctx: Context, message_id: str, reply_data: Dict[str, Any]) -> Dict[str, Any]:
    """Reply to a message.
    
    Args:
        message_id: The message ID
        reply_data: Reply content
    """
    try:
        client = create_graph_client(ctx)
        mail_tools = MailTools(client)
        return await mail_tools.reply_to_message(message_id, reply_data)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in reply_to_message: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def reply_all_to_message(ctx: Context, message_id: str, reply_data: Dict[str, Any]) -> Dict[str, Any]:
    """Reply all to a message.
    
    Args:
        message_id: The message ID
        reply_data: Reply content
    """
    try:
        client = create_graph_client(ctx)
        mail_tools = MailTools(client)
        return await mail_tools.reply_all_to_message(message_id, reply_data)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in reply_all_to_message: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def forward_message(ctx: Context, message_id: str, forward_data: Dict[str, Any]) -> Dict[str, Any]:
    """Forward a message.

    Args:
        message_id: The message ID
        forward_data: Forward content and recipients
    """
    try:
        client = create_graph_client(ctx)
        mail_tools = MailTools(client)
        return await mail_tools.forward_message(message_id, forward_data)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in forward_message: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

# ============================================================================
# MAIL FOLDER TOOLS
# ============================================================================

@mcp.tool()
async def list_mail_folders(ctx: Context, select: Optional[str] = None) -> Dict[str, Any]:
    """List mail folders.

    Args:
        select: Comma-separated list of properties to select
    """
    try:
        client = create_graph_client(ctx)
        mail_tools = MailTools(client)
        return await mail_tools.list_mail_folders(select)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in list_mail_folders: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def get_mail_folder(ctx: Context, folder_id: str, select: Optional[str] = None) -> Dict[str, Any]:
    """Get a specific mail folder.

    Args:
        folder_id: The folder ID
        select: Comma-separated list of properties to select
    """
    try:
        client = create_graph_client(ctx)
        mail_tools = MailTools(client)
        return await mail_tools.get_mail_folder(folder_id, select)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in get_mail_folder: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def create_mail_folder(ctx: Context, folder_data: Dict[str, Any]) -> Dict[str, Any]:
    """Create a new mail folder.

    Args:
        folder_data: Folder data including displayName
    """
    try:
        client = create_graph_client(ctx)
        mail_tools = MailTools(client)
        return await mail_tools.create_mail_folder(folder_data)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in create_mail_folder: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def update_mail_folder(ctx: Context, folder_id: str, update_data: Dict[str, Any]) -> Dict[str, Any]:
    """Update a mail folder.

    Args:
        folder_id: The folder ID
        update_data: Data to update
    """
    try:
        client = create_graph_client(ctx)
        mail_tools = MailTools(client)
        return await mail_tools.update_mail_folder(folder_id, update_data)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in update_mail_folder: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def delete_mail_folder(ctx: Context, folder_id: str) -> Dict[str, Any]:
    """Delete a mail folder.

    Args:
        folder_id: The folder ID
    """
    try:
        client = create_graph_client(ctx)
        mail_tools = MailTools(client)
        return await mail_tools.delete_mail_folder(folder_id)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in delete_mail_folder: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

# ============================================================================
# MESSAGE ATTACHMENT TOOLS
# ============================================================================

@mcp.tool()
async def list_message_attachments(ctx: Context, message_id: str) -> Dict[str, Any]:
    """List attachments for a message.

    Args:
        message_id: The message ID
    """
    try:
        client = create_graph_client(ctx)
        mail_tools = MailTools(client)
        return await mail_tools.list_message_attachments(message_id)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in list_message_attachments: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def get_message_attachment(ctx: Context, message_id: str, attachment_id: str) -> Dict[str, Any]:
    """Get a specific attachment.

    Args:
        message_id: The message ID
        attachment_id: The attachment ID
    """
    try:
        client = create_graph_client(ctx)
        mail_tools = MailTools(client)
        return await mail_tools.get_message_attachment(message_id, attachment_id)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in get_message_attachment: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def add_message_attachment(ctx: Context, message_id: str, attachment_data: Dict[str, Any]) -> Dict[str, Any]:
    """Add an attachment to a message.

    Args:
        message_id: The message ID
        attachment_data: Attachment data
    """
    try:
        client = create_graph_client(ctx)
        mail_tools = MailTools(client)
        return await mail_tools.add_message_attachment(message_id, attachment_data)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in add_message_attachment: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def delete_message_attachment(ctx: Context, message_id: str, attachment_id: str) -> Dict[str, Any]:
    """Delete an attachment from a message.

    Args:
        message_id: The message ID
        attachment_id: The attachment ID
    """
    try:
        client = create_graph_client(ctx)
        mail_tools = MailTools(client)
        return await mail_tools.delete_message_attachment(message_id, attachment_id)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in delete_message_attachment: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

# ============================================================================
# CALENDAR TOOLS
# ============================================================================

@mcp.tool()
async def list_calendars(ctx: Context, select: Optional[str] = None) -> Dict[str, Any]:
    """List user's calendars.

    Args:
        select: Comma-separated list of properties to select
    """
    try:
        client = create_graph_client(ctx)
        calendar_tools = CalendarTools(client)
        return await calendar_tools.list_calendars(select)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in list_calendars: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def get_calendar(ctx: Context, calendar_id: str, select: Optional[str] = None) -> Dict[str, Any]:
    """Get a specific calendar.

    Args:
        calendar_id: The calendar ID
        select: Comma-separated list of properties to select
    """
    try:
        client = create_graph_client(ctx)
        calendar_tools = CalendarTools(client)
        return await calendar_tools.get_calendar(calendar_id, select)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in get_calendar: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def create_calendar(ctx: Context, calendar_data: Dict[str, Any]) -> Dict[str, Any]:
    """Create a new calendar.

    Args:
        calendar_data: Calendar data including name
    """
    try:
        client = create_graph_client(ctx)
        calendar_tools = CalendarTools(client)
        return await calendar_tools.create_calendar(calendar_data)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in create_calendar: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def update_calendar(ctx: Context, calendar_id: str, update_data: Dict[str, Any]) -> Dict[str, Any]:
    """Update a calendar.

    Args:
        calendar_id: The calendar ID
        update_data: Data to update
    """
    try:
        client = create_graph_client(ctx)
        calendar_tools = CalendarTools(client)
        return await calendar_tools.update_calendar(calendar_id, update_data)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in update_calendar: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def delete_calendar(ctx: Context, calendar_id: str) -> Dict[str, Any]:
    """Delete a calendar.

    Args:
        calendar_id: The calendar ID
    """
    try:
        client = create_graph_client(ctx)
        calendar_tools = CalendarTools(client)
        return await calendar_tools.delete_calendar(calendar_id)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in delete_calendar: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def list_events(
    ctx: Context,
    calendar_id: str = "primary",
    select: Optional[str] = None,
    filter_query: Optional[str] = None,
    order_by: Optional[str] = None,
    top: int = 50,
    skip: int = 0
) -> Dict[str, Any]:
    """List events in a calendar.

    Args:
        calendar_id: Calendar ID or 'primary' for default calendar
        select: Comma-separated list of properties to select
        filter_query: OData filter query
        order_by: OData orderby query
        top: Number of items to return
        skip: Number of items to skip
    """
    try:
        client = create_graph_client(ctx)
        calendar_tools = CalendarTools(client)
        return await calendar_tools.list_events(calendar_id, select, filter_query, order_by, top, skip)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in list_events: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def get_event(ctx: Context, event_id: str, calendar_id: str = "primary", select: Optional[str] = None) -> Dict[str, Any]:
    """Get a specific event.

    Args:
        event_id: The event ID
        calendar_id: Calendar ID or 'primary' for default calendar
        select: Comma-separated list of properties to select
    """
    try:
        client = create_graph_client(ctx)
        calendar_tools = CalendarTools(client)
        return await calendar_tools.get_event(event_id, calendar_id, select)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in get_event: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def create_event(ctx: Context, event_data: Dict[str, Any], calendar_id: str = "primary") -> Dict[str, Any]:
    """Create a new event.

    Args:
        event_data: Event data including subject, start, end, etc.
        calendar_id: Calendar ID or 'primary' for default calendar
    """
    try:
        client = create_graph_client(ctx)
        calendar_tools = CalendarTools(client)
        return await calendar_tools.create_event(event_data, calendar_id)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in create_event: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def update_event(ctx: Context, event_id: str, update_data: Dict[str, Any], calendar_id: str = "primary") -> Dict[str, Any]:
    """Update an event.

    Args:
        event_id: The event ID
        update_data: Data to update
        calendar_id: Calendar ID or 'primary' for default calendar
    """
    try:
        client = create_graph_client(ctx)
        calendar_tools = CalendarTools(client)
        return await calendar_tools.update_event(event_id, update_data, calendar_id)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in update_event: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def delete_event(ctx: Context, event_id: str, calendar_id: str = "primary") -> Dict[str, Any]:
    """Delete an event.

    Args:
        event_id: The event ID
        calendar_id: Calendar ID or 'primary' for default calendar
    """
    try:
        client = create_graph_client(ctx)
        calendar_tools = CalendarTools(client)
        return await calendar_tools.delete_event(event_id, calendar_id)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in delete_event: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def accept_event(ctx: Context, event_id: str, comment: Optional[str] = None) -> Dict[str, Any]:
    """Accept a meeting invitation.

    Args:
        event_id: The event ID
        comment: Optional comment
    """
    try:
        client = create_graph_client(ctx)
        calendar_tools = CalendarTools(client)
        return await calendar_tools.accept_event(event_id, comment)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in accept_event: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def decline_event(ctx: Context, event_id: str, comment: Optional[str] = None) -> Dict[str, Any]:
    """Decline a meeting invitation.

    Args:
        event_id: The event ID
        comment: Optional comment
    """
    try:
        client = create_graph_client(ctx)
        calendar_tools = CalendarTools(client)
        return await calendar_tools.decline_event(event_id, comment)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in decline_event: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def tentatively_accept_event(ctx: Context, event_id: str, comment: Optional[str] = None) -> Dict[str, Any]:
    """Tentatively accept a meeting invitation.

    Args:
        event_id: The event ID
        comment: Optional comment
    """
    try:
        client = create_graph_client(ctx)
        calendar_tools = CalendarTools(client)
        return await calendar_tools.tentatively_accept_event(event_id, comment)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in tentatively_accept_event: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def get_calendar_view(
    ctx: Context,
    start_time: str,
    end_time: str,
    calendar_id: str = "primary",
    select: Optional[str] = None,
    top: int = 50
) -> Dict[str, Any]:
    """Get calendar view for a specific time range.

    Args:
        start_time: Start time in ISO format
        end_time: End time in ISO format
        calendar_id: Calendar ID or 'primary' for default calendar
        select: Comma-separated list of properties to select
        top: Number of items to return
    """
    try:
        client = create_graph_client(ctx)
        calendar_tools = CalendarTools(client)
        return await calendar_tools.get_calendar_view(start_time, end_time, calendar_id, select, top)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in get_calendar_view: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def get_free_busy(ctx: Context, schedules: List[str], start_time: str, end_time: str) -> Dict[str, Any]:
    """Get free/busy information for users.

    Args:
        schedules: List of user email addresses
        start_time: Start time in ISO format
        end_time: End time in ISO format
    """
    try:
        client = create_graph_client(ctx)
        calendar_tools = CalendarTools(client)
        return await calendar_tools.get_free_busy(schedules, start_time, end_time)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in get_free_busy: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

# ============================================================================
# CONTACTS TOOLS
# ============================================================================

@mcp.tool()
async def list_contacts(
    ctx: Context,
    folder_id: Optional[str] = None,
    select: Optional[str] = None,
    filter_query: Optional[str] = None,
    order_by: Optional[str] = None,
    top: int = 50,
    skip: int = 0
) -> Dict[str, Any]:
    """List contacts.

    Args:
        folder_id: Contact folder ID (optional)
        select: Comma-separated list of properties to select
        filter_query: OData filter query
        order_by: OData orderby query
        top: Number of items to return
        skip: Number of items to skip
    """
    try:
        client = create_graph_client(ctx)
        contacts_tools = ContactsTools(client)
        return await contacts_tools.list_contacts(folder_id, select, filter_query, order_by, top, skip)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in list_contacts: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def get_contact(ctx: Context, contact_id: str, select: Optional[str] = None) -> Dict[str, Any]:
    """Get a specific contact.

    Args:
        contact_id: The contact ID
        select: Comma-separated list of properties to select
    """
    try:
        client = create_graph_client(ctx)
        contacts_tools = ContactsTools(client)
        return await contacts_tools.get_contact(contact_id, select)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in get_contact: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def create_contact(ctx: Context, contact_data: Dict[str, Any], folder_id: Optional[str] = None) -> Dict[str, Any]:
    """Create a new contact.

    Args:
        contact_data: Contact data
        folder_id: Contact folder ID (optional)
    """
    try:
        client = create_graph_client(ctx)
        contacts_tools = ContactsTools(client)
        return await contacts_tools.create_contact(contact_data, folder_id)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in create_contact: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def update_contact(ctx: Context, contact_id: str, update_data: Dict[str, Any]) -> Dict[str, Any]:
    """Update a contact.

    Args:
        contact_id: The contact ID
        update_data: Data to update
    """
    try:
        client = create_graph_client(ctx)
        contacts_tools = ContactsTools(client)
        return await contacts_tools.update_contact(contact_id, update_data)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in update_contact: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def delete_contact(ctx: Context, contact_id: str) -> Dict[str, Any]:
    """Delete a contact.

    Args:
        contact_id: The contact ID
    """
    try:
        client = create_graph_client(ctx)
        contacts_tools = ContactsTools(client)
        return await contacts_tools.delete_contact(contact_id)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in delete_contact: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def list_contact_folders(ctx: Context, select: Optional[str] = None) -> Dict[str, Any]:
    """List contact folders.

    Args:
        select: Comma-separated list of properties to select
    """
    try:
        client = create_graph_client(ctx)
        contacts_tools = ContactsTools(client)
        return await contacts_tools.list_contact_folders(select)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in list_contact_folders: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

@mcp.tool()
async def search_contacts(ctx: Context, query: str, top: int = 50) -> Dict[str, Any]:
    """Search contacts by query.

    Args:
        query: Search query
        top: Number of items to return
    """
    try:
        client = create_graph_client(ctx)
        contacts_tools = ContactsTools(client)
        return await contacts_tools.search_contacts(query, top)
    except GraphAPIError as e:
        raise FastMCPError( f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in search_contacts: {e}")
        raise FastMCPError( f"Unexpected error: {e}")

# ============================================================================
# RESOURCES
# ============================================================================

@mcp.resource("outlook://user/profile")
async def get_user_profile_resource() -> str:
    """Get user profile information as a resource."""
    try:
        ctx = mcp.get_context()
        client = create_graph_client(ctx)
        resources = OutlookResources(client)
        data = await resources.get_user_profile()
        return f"User Profile:\n{data}"
    except GraphAPIError as e:
        raise FastMCPError(f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in get_user_profile_resource: {e}")
        raise FastMCPError(f"Unexpected error: {e}")

@mcp.resource("outlook://mailbox/settings")
async def get_mailbox_settings_resource() -> str:
    """Get mailbox settings as a resource."""
    try:
        ctx = mcp.get_context()
        client = create_graph_client(ctx)
        resources = OutlookResources(client)
        data = await resources.get_mailbox_settings()
        return f"Mailbox Settings:\n{data}"
    except GraphAPIError as e:
        raise FastMCPError(f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in get_mailbox_settings_resource: {e}")
        raise FastMCPError(f"Unexpected error: {e}")

@mcp.resource("outlook://mail/folders")
async def get_mail_folder_structure_resource() -> str:
    """Get mail folder structure as a resource."""
    try:
        ctx = mcp.get_context()
        client = create_graph_client(ctx)
        resources = OutlookResources(client)
        data = await resources.get_mail_folder_structure()
        return f"Mail Folder Structure:\n{data}"
    except GraphAPIError as e:
        raise FastMCPError(f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in get_mail_folder_structure_resource: {e}")
        raise FastMCPError(f"Unexpected error: {e}")

@mcp.resource("outlook://calendars")
async def get_calendar_list_resource() -> str:
    """Get calendar list as a resource."""
    try:
        ctx = mcp.get_context()
        client = create_graph_client(ctx)
        resources = OutlookResources(client)
        data = await resources.get_calendar_list()
        return f"Calendar List:\n{data}"
    except GraphAPIError as e:
        raise FastMCPError(f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in get_calendar_list_resource: {e}")
        raise FastMCPError(f"Unexpected error: {e}")

@mcp.resource("outlook://categories")
async def get_outlook_categories_resource() -> str:
    """Get Outlook categories as a resource."""
    try:
        ctx = mcp.get_context()
        client = create_graph_client(ctx)
        resources = OutlookResources(client)
        data = await resources.get_outlook_categories()
        return f"Outlook Categories:\n{data}"
    except GraphAPIError as e:
        raise FastMCPError(f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in get_outlook_categories_resource: {e}")
        raise FastMCPError(f"Unexpected error: {e}")

@mcp.resource("outlook://contacts/folders")
async def get_contact_folder_structure_resource() -> str:
    """Get contact folder structure as a resource."""
    try:
        ctx = mcp.get_context()
        client = create_graph_client(ctx)
        resources = OutlookResources(client)
        data = await resources.get_contact_folder_structure()
        return f"Contact Folder Structure:\n{data}"
    except GraphAPIError as e:
        raise FastMCPError(f"Graph API error: {e}")
    except Exception as e:
        logger.error(f"Error in get_contact_folder_structure_resource: {e}")
        raise FastMCPError(f"Unexpected error: {e}")

# ============================================================================
# MAIN FUNCTION
# ============================================================================

if __name__ == "__main__":
    logger.info("Starting MCP Outlook 365 Server...")
    logger.info(f"Required scopes: {', '.join(ALL_SCOPES)}")
    mcp.run()
