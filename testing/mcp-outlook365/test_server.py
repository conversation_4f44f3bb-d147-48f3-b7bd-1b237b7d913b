"""
Test script for MCP Outlook 365 Server

This script performs basic validation of the server setup.
"""

import sys
import importlib.util
import logging
from pathlib import Path

def test_imports():
    """Test that all required modules can be imported."""
    print("Testing imports...")
    
    try:
        # Test MCP imports
        from mcp.server.fastmcp import FastMCP, Context
        from mcp.server.fastmcp.exceptions import FastMCPError
        print("✓ MCP imports successful")
        
        # Test HTTP client
        import httpx
        print("✓ httpx import successful")
        
        # Test our modules
        from graph_client import GraphClient, GraphAPIError
        print("✓ graph_client import successful")
        
        from tools.mail_tools import MailTools
        from tools.calendar_tools import CalendarTools
        from tools.contacts_tools import ContactsTools
        print("✓ tools imports successful")
        
        from resources.outlook_resources import OutlookResources
        print("✓ resources import successful")
        
        import config
        print("✓ config import successful")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False

def test_server_creation():
    """Test that the MCP server can be created."""
    print("\nTesting server creation...")
    
    try:
        # Import the server module
        import server
        print("✓ Server module imported successfully")
        
        # Check that the mcp instance exists
        if hasattr(server, 'mcp'):
            print("✓ MCP server instance created")
            return True
        else:
            print("✗ MCP server instance not found")
            return False
            
    except Exception as e:
        print(f"✗ Server creation error: {e}")
        return False

def test_configuration():
    """Test configuration values."""
    print("\nTesting configuration...")
    
    try:
        import config
        
        # Check required constants
        required_attrs = [
            'GRAPH_BASE_URL',
            'MAIL_SCOPES',
            'CALENDAR_SCOPES', 
            'CONTACTS_SCOPES',
            'ALL_SCOPES',
            'WELL_KNOWN_FOLDERS',
            'COMMON_SELECT_FIELDS'
        ]
        
        for attr in required_attrs:
            if hasattr(config, attr):
                print(f"✓ {attr} defined")
            else:
                print(f"✗ {attr} missing")
                return False
        
        # Check that scopes are not empty
        if config.ALL_SCOPES:
            print(f"✓ {len(config.ALL_SCOPES)} scopes configured")
        else:
            print("✗ No scopes configured")
            return False
            
        return True
        
    except Exception as e:
        print(f"✗ Configuration error: {e}")
        return False

def test_graph_client():
    """Test GraphClient class."""
    print("\nTesting GraphClient...")
    
    try:
        from graph_client import GraphClient, GraphAPIError
        
        # Test client creation with dummy token
        client = GraphClient("dummy_token")
        print("✓ GraphClient created successfully")
        
        # Check that required methods exist
        required_methods = [
            'get', 'post', 'patch', 'put', 'delete', 'get_paginated'
        ]
        
        for method in required_methods:
            if hasattr(client, method):
                print(f"✓ {method} method exists")
            else:
                print(f"✗ {method} method missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ GraphClient error: {e}")
        return False

def test_tools():
    """Test tool classes."""
    print("\nTesting tool classes...")
    
    try:
        from graph_client import GraphClient
        from tools.mail_tools import MailTools
        from tools.calendar_tools import CalendarTools
        from tools.contacts_tools import ContactsTools
        
        # Create dummy client
        client = GraphClient("dummy_token")
        
        # Test tool creation
        mail_tools = MailTools(client)
        print("✓ MailTools created")
        
        calendar_tools = CalendarTools(client)
        print("✓ CalendarTools created")
        
        contacts_tools = ContactsTools(client)
        print("✓ ContactsTools created")
        
        return True
        
    except Exception as e:
        print(f"✗ Tools error: {e}")
        return False

def test_resources():
    """Test resource classes."""
    print("\nTesting resource classes...")
    
    try:
        from graph_client import GraphClient
        from resources.outlook_resources import OutlookResources
        
        # Create dummy client
        client = GraphClient("dummy_token")
        
        # Test resource creation
        resources = OutlookResources(client)
        print("✓ OutlookResources created")
        
        return True
        
    except Exception as e:
        print(f"✗ Resources error: {e}")
        return False

def check_file_structure():
    """Check that all required files exist."""
    print("\nChecking file structure...")
    
    required_files = [
        "server.py",
        "graph_client.py", 
        "config.py",
        "requirements.txt",
        "README.md",
        "tools/__init__.py",
        "tools/mail_tools.py",
        "tools/calendar_tools.py",
        "tools/contacts_tools.py",
        "resources/__init__.py",
        "resources/outlook_resources.py"
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path} missing")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def main():
    """Run all tests."""
    print("=== MCP Outlook 365 Server Test Suite ===\n")
    
    tests = [
        ("File Structure", check_file_structure),
        ("Imports", test_imports),
        ("Configuration", test_configuration),
        ("GraphClient", test_graph_client),
        ("Tools", test_tools),
        ("Resources", test_resources),
        ("Server Creation", test_server_creation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*50)
    print("TEST SUMMARY")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{total}")
    
    if passed == total:
        print("\n🎉 All tests passed! The server is ready to use.")
        return 0
    else:
        print(f"\n❌ {total - passed} test(s) failed. Please fix the issues before using the server.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
