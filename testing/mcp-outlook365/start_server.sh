#!/bin/bash

# MCP Outlook 365 Server Startup Script

echo "Starting MCP Outlook 365 Server..."

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Virtual environment not found. Creating one..."
    python3.12 -m venv venv
    echo "Installing dependencies..."
    source venv/bin/activate
    pip install -r requirements.txt
else
    echo "Activating virtual environment..."
    source venv/bin/activate
fi

# Check if dependencies are installed
echo "Checking dependencies..."
python -c "import mcp.server.fastmcp" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "MCP dependencies not found. Installing..."
    pip install -r requirements.txt
fi

echo "Required scopes for Azure AD application:"
echo "- Mail.Read"
echo "- Mail.ReadWrite" 
echo "- Mail.Send"
echo "- MailboxSettings.Read"
echo "- MailboxSettings.ReadWrite"
echo "- Calendars.Read"
echo "- Calendars.ReadWrite"
echo "- Calendars.Read.Shared"
echo "- Calendars.ReadWrite.Shared"
echo "- Contacts.Read"
echo "- Contacts.ReadWrite"
echo ""
echo "Make sure your frontend application passes the access token in the Authorization header:"
echo "Authorization: Bearer <access_token>"
echo ""
echo "Starting server..."

# Start the MCP server
python server.py
