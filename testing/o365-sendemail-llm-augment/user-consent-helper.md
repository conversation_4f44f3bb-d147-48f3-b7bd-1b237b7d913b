# User Consent Helper for OBO Flow

## The Issue
For On-Behalf-Of (OBO) flow to work with user consent only, the user must consent to the **backend API** accessing Microsoft Graph on their behalf.

## Solution: Manual User Consent

Since you want user consent only (no admin consent), each user needs to consent to the backend API's Microsoft Graph permissions.

### Option 1: Consent URL (Recommended)

Create this URL and have each user visit it once:

```
https://login.microsoftonline.com/{TENANT_ID}/oauth2/v2.0/authorize?
client_id={BACKEND_API_CLIENT_ID}&
response_type=code&
redirect_uri=https://localhost&
scope=https://graph.microsoft.com/Mail.Send&
response_mode=query&
prompt=consent
```

**Replace the placeholders:**
- `{TENANT_ID}`: `42cc3295-cd0e-449c-b98e-5ce5b560c1d3`
- `{BACKEND_API_CLIENT_ID}`: `2c0cbcbe-7d89-448d-a88e-02453f272ee3`

**Full URL for your setup:**
```
https://login.microsoftonline.com/42cc3295-cd0e-449c-b98e-5ce5b560c1d3/oauth2/v2.0/authorize?client_id=2c0cbcbe-7d89-448d-a88e-02453f272ee3&response_type=code&redirect_uri=https://localhost&scope=https://graph.microsoft.com/Mail.Send&response_mode=query&prompt=consent
```

### Option 2: PowerShell Script

```powershell
# Install Microsoft Graph PowerShell if not already installed
Install-Module Microsoft.Graph -Scope CurrentUser

# Connect and consent
Connect-MgGraph -Scopes "Mail.Send" -ClientId "2c0cbcbe-7d89-448d-a88e-02453f272ee3" -TenantId "42cc3295-cd0e-449c-b98e-5ce5b560c1d3"
```

### Option 3: Add Consent to Your App

Add a "Grant Backend Permissions" button that opens this URL in a popup.

## How It Works

1. **User visits consent URL** → Consents to backend API accessing Graph
2. **User uses your app** → Frontend gets token for backend API
3. **Backend receives token** → Uses OBO to exchange for Graph token
4. **Backend sends email** → Using Graph API on user's behalf

## Verification

After user consents, you can verify by:

1. **Check Azure Portal** → Enterprise Applications → Backend API → Users and groups
2. **Look for the user** in the consented users list
3. **Test the email sending** in your application

## Why This Works

- **Frontend app**: User consents to access backend API ✅
- **Backend API**: User consents to access Microsoft Graph ✅  
- **OBO flow**: Can now exchange frontend token for Graph token ✅
- **No admin consent**: Everything uses delegated permissions ✅

This approach maintains user-level consent while enabling the backend to send emails on behalf of users.
