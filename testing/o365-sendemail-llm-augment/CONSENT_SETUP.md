# User Consent Setup for AI Chat Assistant

This guide explains how to set up user consent URLs so end users can grant all necessary permissions for the AI Chat Assistant application.

## 🎯 Overview

The AI Chat Assistant requires several Microsoft Graph permissions to work with Outlook 365:
- **Email**: Read, write, and send emails
- **Calendar**: Read and write calendar events  
- **Contacts**: Read and write contacts
- **Profile**: Read user profile information

## 🔧 Step 1: Update Azure App Registration

Before generating consent URLs, you need to add the required permissions to your Azure app registration.

### 1.1 Add Microsoft Graph Permissions

1. Go to [Azure Portal](https://portal.azure.com)
2. Navigate to **Azure Active Directory** > **App registrations**
3. Select your **backend API app registration** (`o365-email-api`)
4. Go to **API permissions**
5. Click **Add a permission**
6. Select **Microsoft Graph** > **Delegated permissions**
7. Add these permissions:

```
✅ Mail.Read - Read user mail
✅ Mail.ReadWrite - Read and write access to user mail  
✅ Mail.Send - Send mail as a user
✅ Calendars.Read - Read user calendars
✅ Calendars.ReadWrite - Read and write user calendars
✅ Contacts.Read - Read user contacts
✅ Contacts.ReadWrite - Read and write user contacts
✅ User.Read - Sign in and read user profile
```

8. Click **Add permissions** for each one

### 1.2 Configure Consent Settings

1. In **API permissions**, ensure **User consent** is allowed for each permission
2. Do **NOT** click "Grant admin consent" if you want individual user consent
3. If you want tenant-wide consent, you can click "Grant admin consent for [Your Org]"

## 🔗 Step 2: Generate Consent URLs

Use the provided script to generate consent URLs:

```bash
python generate_consent_url.py
```

This will output three types of URLs:

### 🏢 Admin Consent URL (Tenant-wide)
Use this if you want to grant permissions for ALL users in your tenant:

```
https://login.microsoftonline.com/42cc3295-cd0e-449c-b98e-5ce5b560c1d3/v2.0/adminconsent?client_id=2c0cbcbe-7d89-448d-a88e-02453f272ee3&response_type=code&redirect_uri=https%3A%2F%2Flocalhost&response_mode=query&scope=https%3A%2F%2Fgraph.microsoft.com%2F.default&state=admin_consent
```

**Who can use this**: Tenant administrators only
**Effect**: Grants permissions for all users in the organization

### 👤 User Consent URL (Individual users)
Use this for individual users to grant their own permissions:

```
https://login.microsoftonline.com/42cc3295-cd0e-449c-b98e-5ce5b560c1d3/oauth2/v2.0/authorize?client_id=6261b1c1-29e2-4e6c-99c2-fb285c7de4d0&response_type=code&redirect_uri=http%3A%2F%2Flocalhost%3A3000&response_mode=query&scope=openid+profile+offline_access+https%3A%2F%2Fgraph.microsoft.com%2FMail.Read+https%3A%2F%2Fgraph.microsoft.com%2FMail.ReadWrite+https%3A%2F%2Fgraph.microsoft.com%2FMail.Send+https%3A%2F%2Fgraph.microsoft.com%2FCalendars.Read+https%3A%2F%2Fgraph.microsoft.com%2FCalendars.ReadWrite+https%3A%2F%2Fgraph.microsoft.com%2FContacts.Read+https%3A%2F%2Fgraph.microsoft.com%2FContacts.ReadWrite+https%3A%2F%2Fgraph.microsoft.com%2FUser.Read&state=user_consent&prompt=consent
```

**Who can use this**: Any user in your organization
**Effect**: Grants permissions only for that specific user

### 🔌 API Consent URL (Backend access)
Use this to grant access to the backend API:

```
https://login.microsoftonline.com/42cc3295-cd0e-449c-b98e-5ce5b560c1d3/oauth2/v2.0/authorize?client_id=6261b1c1-29e2-4e6c-99c2-fb285c7de4d0&response_type=code&redirect_uri=http%3A%2F%2Flocalhost%3A3000&response_mode=query&scope=api%3A%2F%2F2c0cbcbe-7d89-448d-a88e-02453f272ee3%2Faccess_as_user+openid+profile&state=api_consent&prompt=consent
```

**Who can use this**: Any user in your organization
**Effect**: Grants access to the backend API

## 📋 Step 3: How to Use the URLs

### For Administrators (Tenant-wide consent):
1. Open the **Admin Consent URL** in a browser
2. Sign in with your admin account
3. Review the permissions requested
4. Click **Accept** to grant permissions for all users
5. All users can now use the application without individual consent

### For Individual Users:
1. Send users the **User Consent URL** 
2. Users open the URL in their browser
3. Users sign in with their work account
4. Users review and accept the permissions
5. Users can now use the AI chat features

### For API Access:
1. Users may also need to use the **API Consent URL**
2. This grants access to the backend API specifically
3. Follow the same process as user consent

## 🎯 Step 4: Integration with Your Application

### Option 1: Add Consent Button to Your App

You can add a "Grant Permissions" button to your application:

```javascript
// In your React component
const handleGrantPermissions = () => {
  const consentUrl = "YOUR_USER_CONSENT_URL_HERE";
  window.location.href = consentUrl;
};

return (
  <button onClick={handleGrantPermissions}>
    Grant AI Chat Permissions
  </button>
);
```

### Option 2: Automatic Consent Check

The application already includes automatic consent checking. When users try to use AI features, they'll be prompted to grant permissions if needed.

## 🔍 Step 5: Verify Permissions

After consent is granted, you can verify in Azure Portal:

1. Go to **Azure Active Directory** > **Enterprise applications**
2. Find your application
3. Go to **Users and groups** to see who has consented
4. Go to **Permissions** to see granted permissions

## 🚨 Troubleshooting

### Common Issues:

**"AADSTS65001: The user or administrator has not consented"**
- User needs to use the consent URL first
- Or admin needs to grant tenant-wide consent

**"AADSTS50011: Reply URL mismatch"**
- Check that redirect URIs match in app registration
- Ensure you're using the correct URL (http vs https)

**"AADSTS70011: Invalid scope"**
- Verify all permissions are added to the app registration
- Check that permission names are correct

**Permissions not working after consent**
- Wait a few minutes for permissions to propagate
- Try signing out and back in to the application
- Check that the correct client ID is being used

## 📧 Sharing with End Users

You can share this information with your end users:

---

**Subject: AI Chat Assistant - Permission Setup Required**

Hi [User],

To use the new AI Chat Assistant with Outlook 365 integration, you need to grant some permissions first.

**What you need to do:**
1. Click this link: [USER_CONSENT_URL]
2. Sign in with your work account
3. Review and accept the permissions
4. Return to the application and start chatting!

**What permissions are requested:**
- Read and send emails on your behalf
- Read and manage your calendar
- Access your contacts
- Read your profile information

These permissions allow the AI assistant to help you with email, calendar, and contact management through natural language commands.

If you have any questions, please contact [Your IT Team].

---

## 🔒 Security Notes

- Permissions are granted per user (unless admin consent is used)
- Users can revoke permissions at any time in their account settings
- All permissions are "delegated" - the app can only act on behalf of the signed-in user
- No data is stored permanently; all operations are performed in real-time

## 📚 Additional Resources

- [Microsoft Graph Permissions Reference](https://docs.microsoft.com/en-us/graph/permissions-reference)
- [Azure AD Consent Framework](https://docs.microsoft.com/en-us/azure/active-directory/develop/consent-framework)
- [Delegated vs Application Permissions](https://docs.microsoft.com/en-us/azure/active-directory/develop/v2-permissions-and-consent)
