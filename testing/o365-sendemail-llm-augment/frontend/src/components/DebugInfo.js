import React, { useState } from 'react';
import { useMsal } from '@azure/msal-react';
import { Card, Button, Alert } from 'react-bootstrap';
import { apiConfig } from '../config';

function DebugInfo() {
  const { instance, accounts } = useMsal();
  const [debugInfo, setDebugInfo] = useState('');
  const [showDebug, setShowDebug] = useState(false);

  const getDebugInfo = async () => {
    try {
      const account = accounts[0];
      const request = {
        scopes: apiConfig.scopes,
        account: account
      };

      // Try to get token silently
      let tokenInfo = {};
      try {
        const response = await instance.acquireTokenSilent(request);
        tokenInfo = {
          hasToken: true,
          scopes: response.scopes,
          account: response.account.username,
          tokenType: 'Silent acquisition successful'
        };
      } catch (error) {
        tokenInfo = {
          hasToken: false,
          error: error.message,
          errorCode: error.errorCode,
          tokenType: 'Silent acquisition failed'
        };
      }

      const info = {
        environment: {
          clientId: process.env.REACT_APP_CLIENT_ID,
          tenantId: process.env.REACT_APP_TENANT_ID,
          apiClientId: process.env.REACT_APP_API_CLIENT_ID,
          apiBaseUrl: process.env.REACT_APP_API_BASE_URL
        },
        requestedScopes: apiConfig.scopes,
        account: {
          username: account?.username,
          name: account?.name,
          tenantId: account?.tenantId
        },
        token: tokenInfo
      };

      setDebugInfo(JSON.stringify(info, null, 2));
      setShowDebug(true);
    } catch (error) {
      setDebugInfo(`Error getting debug info: ${error.message}`);
      setShowDebug(true);
    }
  };

  if (!showDebug) {
    return (
      <Card className="mt-3">
        <Card.Body>
          <Button variant="outline-secondary" size="sm" onClick={getDebugInfo}>
            🔍 Show Debug Info
          </Button>
        </Card.Body>
      </Card>
    );
  }

  return (
    <Card className="mt-3">
      <Card.Header>
        <div className="d-flex justify-content-between align-items-center">
          <h6>🔍 Debug Information</h6>
          <Button variant="outline-secondary" size="sm" onClick={() => setShowDebug(false)}>
            Hide
          </Button>
        </div>
      </Card.Header>
      <Card.Body>
        <Alert variant="info">
          <small>
            This information can help diagnose authentication issues. 
            Share this with support if you need help.
          </small>
        </Alert>
        <pre style={{ fontSize: '12px', maxHeight: '300px', overflow: 'auto' }}>
          {debugInfo}
        </pre>
        <Button 
          variant="outline-primary" 
          size="sm" 
          onClick={() => navigator.clipboard.writeText(debugInfo)}
        >
          📋 Copy to Clipboard
        </Button>
      </Card.Body>
    </Card>
  );
}

export default DebugInfo;
