import React, { useState } from 'react';
import { useMsal } from '@azure/msal-react';
import { Button, <PERSON>ert, Card } from 'react-bootstrap';
import { apiConfig } from '../config';

function ConsentHelper() {
  const { instance, accounts } = useMsal();
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });

  const handleGrantConsent = async () => {
    setLoading(true);
    setMessage({ type: '', text: '' });

    try {
      const consentRequest = {
        scopes: apiConfig.scopes,
        account: accounts[0],
        prompt: 'consent' // Force consent screen
      };

      const response = await instance.acquireTokenPopup(consentRequest);
      console.log('Consent granted successfully:', response);
      
      setMessage({
        type: 'success',
        text: '✅ Consent granted successfully! You can now send emails.'
      });

    } catch (error) {
      console.error('Consent failed:', error);
      setMessage({
        type: 'danger',
        text: `❌ Consent failed: ${error.message || 'Unknown error'}`
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="mt-3">
      <Card.Header>
        <h5>🔐 Grant API Permissions</h5>
      </Card.Header>
      <Card.Body>
        <p>
          To send emails on your behalf, this application needs your consent to access 
          the email API. Click the button below to grant the necessary permissions.
        </p>
        
        <Button
          variant="warning"
          onClick={handleGrantConsent}
          disabled={loading}
          className="mb-3"
        >
          {loading ? (
            <>
              <span className="loading-spinner me-2"></span>
              Requesting Consent...
            </>
          ) : (
            '🔓 Grant API Consent'
          )}
        </Button>

        {message.text && (
          <Alert variant={message.type}>
            {message.text}
          </Alert>
        )}

        <div className="mt-3">
          <small className="text-muted">
            <strong>What permissions are being requested?</strong>
            <ul className="mt-2">
              <li>Access to send emails on your behalf</li>
              <li>No access to read your emails or other data</li>
              <li>Permissions are granted only for your account</li>
            </ul>
          </small>
        </div>
      </Card.Body>
    </Card>
  );
}

export default ConsentHelper;
