import React, { useState, useRef, useEffect } from 'react';
import { Form, Button, InputGroup } from 'react-bootstrap';

const MessageInput = ({ onSendMessage, disabled, placeholder }) => {
  const [message, setMessage] = useState('');
  const textareaRef = useRef(null);

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }
  }, [message]);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (message.trim() && !disabled) {
      onSendMessage(message);
      setMessage('');
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleChange = (e) => {
    setMessage(e.target.value);
  };

  // Suggested prompts for quick access
  const suggestedPrompts = [
    "Check my recent emails",
    "What meetings do I have today?",
    "Send an email to my team",
    "Create a calendar event",
    "Find contacts with 'Smith' in the name",
    "Show me my inbox folders"
  ];

  const handleSuggestedPrompt = (prompt) => {
    if (!disabled) {
      setMessage(prompt);
      textareaRef.current?.focus();
    }
  };

  return (
    <div className="message-input">
      {/* Suggested prompts */}
      <div className="suggested-prompts mb-2">
        <small className="text-muted">Quick suggestions:</small>
        <div className="d-flex flex-wrap gap-1 mt-1">
          {suggestedPrompts.map((prompt, index) => (
            <Button
              key={index}
              variant="outline-secondary"
              size="sm"
              className="suggested-prompt-btn"
              onClick={() => handleSuggestedPrompt(prompt)}
              disabled={disabled}
            >
              {prompt}
            </Button>
          ))}
        </div>
      </div>

      {/* Message input form */}
      <Form onSubmit={handleSubmit}>
        <InputGroup>
          <Form.Control
            ref={textareaRef}
            as="textarea"
            rows={1}
            value={message}
            onChange={handleChange}
            onKeyPress={handleKeyPress}
            placeholder={placeholder || "Type your message here..."}
            disabled={disabled}
            className="message-textarea"
            style={{
              resize: 'none',
              minHeight: '38px',
              maxHeight: '120px'
            }}
          />
          <Button
            type="submit"
            variant="primary"
            disabled={disabled || !message.trim()}
            className="send-button"
          >
            {disabled ? (
              <span className="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
            ) : (
              '📤'
            )}
            Send
          </Button>
        </InputGroup>
      </Form>

      {/* Help text */}
      <div className="input-help-text mt-1">
        <small className="text-muted">
          💡 I can help you with emails, calendar events, contacts, and more. 
          Press Enter to send, Shift+Enter for new line.
        </small>
      </div>
    </div>
  );
};

export default MessageInput;
