import React from 'react';
import { useMsal } from '@azure/msal-react';
import { Button } from 'react-bootstrap';
import { loginRequest } from '../config';

function LoginButton() {
  const { instance } = useMsal();

  const handleLogin = () => {
    instance.loginPopup(loginRequest).catch(e => {
      console.error('<PERSON><PERSON> failed:', e);
    });
  };

  return (
    <Button variant="primary" onClick={handleLogin} size="lg">
      🔐 Sign in with Microsoft
    </Button>
  );
}

export default LoginButton;
