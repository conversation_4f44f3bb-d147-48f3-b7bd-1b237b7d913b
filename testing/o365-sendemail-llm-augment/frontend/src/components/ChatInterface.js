import React, { useState, useRef, useEffect } from 'react';
import { Con<PERSON><PERSON>, <PERSON>, Col, <PERSON>, Al<PERSON>, Spinner } from 'react-bootstrap';
import { useMsal } from '@azure/msal-react';
import axios from 'axios';
import MessageList from './MessageList';
import MessageInput from './MessageInput';
import { apiConfig } from '../config';

const ChatInterface = () => {
  const { instance, accounts } = useMsal();
  const [messages, setMessages] = useState([
    {
      id: 1,
      role: 'assistant',
      content: 'Hello! I\'m your AI assistant with access to Microsoft Outlook 365. I can help you with emails, calendar events, contacts, and more. What would you like to do today?',
      timestamp: new Date().toISOString(),
      toolCalls: []
    }
  ]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const messagesEndRef = useRef(null);

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const getAccessToken = async () => {
    try {
      const request = {
        scopes: apiConfig.scopes,
        account: accounts[0]
      };
      const response = await instance.acquireTokenSilent(request);
      return response.accessToken;
    } catch (error) {
      console.error('Failed to acquire token:', error);
      throw new Error('Authentication failed. Please try logging in again.');
    }
  };

  const sendMessage = async (content) => {
    if (!content.trim()) return;

    // Add user message to the chat
    const userMessage = {
      id: Date.now(),
      role: 'user',
      content: content.trim(),
      timestamp: new Date().toISOString(),
      toolCalls: []
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);
    setError(null);

    try {
      // Get access token
      const accessToken = await getAccessToken();

      // Prepare messages for API (exclude IDs and toolCalls)
      const apiMessages = [...messages, userMessage].map(msg => ({
        role: msg.role,
        content: msg.content
      }));

      // Send to backend
      const response = await axios.post(
        `${process.env.REACT_APP_API_URL || 'http://localhost:8000'}/chat`,
        {
          messages: apiMessages,
          max_iterations: 5
        },
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      // Add assistant response
      const assistantMessage = {
        id: Date.now() + 1,
        role: 'assistant',
        content: response.data.response,
        timestamp: new Date().toISOString(),
        toolCalls: response.data.tool_calls || [],
        success: response.data.success,
        note: response.data.note
      };

      setMessages(prev => [...prev, assistantMessage]);

    } catch (error) {
      console.error('Chat error:', error);
      
      let errorMessage = 'Sorry, I encountered an error while processing your request.';
      
      if (error.response) {
        // Server responded with error status
        if (error.response.status === 401) {
          errorMessage = 'Authentication failed. Please try refreshing the page and logging in again.';
        } else if (error.response.status === 500) {
          errorMessage = 'Server error occurred. Please try again in a moment.';
        } else if (error.response.data?.detail) {
          errorMessage = `Error: ${error.response.data.detail}`;
        }
      } else if (error.request) {
        // Network error
        errorMessage = 'Network error. Please check your connection and try again.';
      }

      setError(errorMessage);

      // Add error message to chat
      const errorChatMessage = {
        id: Date.now() + 1,
        role: 'assistant',
        content: errorMessage,
        timestamp: new Date().toISOString(),
        toolCalls: [],
        isError: true
      };

      setMessages(prev => [...prev, errorChatMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const clearChat = () => {
    setMessages([
      {
        id: 1,
        role: 'assistant',
        content: 'Hello! I\'m your AI assistant with access to Microsoft Outlook 365. I can help you with emails, calendar events, contacts, and more. What would you like to do today?',
        timestamp: new Date().toISOString(),
        toolCalls: []
      }
    ]);
    setError(null);
  };

  return (
    <Container fluid className="chat-interface">
      <Row className="h-100">
        <Col>
          <Card className="chat-card h-100">
            <Card.Header className="d-flex justify-content-between align-items-center">
              <h4 className="mb-0">
                🤖 AI Assistant with Outlook 365 Integration
              </h4>
              <div>
                <button 
                  className="btn btn-outline-secondary btn-sm me-2"
                  onClick={clearChat}
                  disabled={isLoading}
                >
                  Clear Chat
                </button>
                {isLoading && (
                  <Spinner animation="border" size="sm" variant="primary" />
                )}
              </div>
            </Card.Header>
            
            <Card.Body className="chat-body d-flex flex-column p-0">
              {error && (
                <Alert variant="danger" className="m-3 mb-0" dismissible onClose={() => setError(null)}>
                  {error}
                </Alert>
              )}
              
              <div className="messages-container flex-grow-1">
                <MessageList messages={messages} />
                <div ref={messagesEndRef} />
              </div>
              
              <div className="message-input-container">
                <MessageInput 
                  onSendMessage={sendMessage}
                  disabled={isLoading}
                  placeholder={isLoading ? "Processing your request..." : "Type your message here..."}
                />
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default ChatInterface;
