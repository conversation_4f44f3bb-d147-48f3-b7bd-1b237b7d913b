import React from 'react';
import { Badge, Accordion, Card } from 'react-bootstrap';

const MessageList = ({ messages }) => {
  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const formatToolCall = (toolCall, index) => {
    return (
      <Accordion key={index} className="mt-2">
        <Accordion.Item eventKey={index.toString()}>
          <Accordion.Header>
            <div className="d-flex align-items-center">
              <Badge bg={toolCall.result?.success ? "success" : "danger"} className="me-2">
                🔧
              </Badge>
              <span className="fw-bold">{toolCall.tool}</span>
              {toolCall.result?.success === false && (
                <Badge bg="warning" className="ms-2">Failed</Badge>
              )}
            </div>
          </Accordion.Header>
          <Accordion.Body>
            <div className="tool-call-details">
              <div className="mb-2">
                <strong>Arguments:</strong>
                <pre className="bg-light p-2 mt-1 rounded">
                  {JSON.stringify(toolCall.arguments, null, 2)}
                </pre>
              </div>
              <div>
                <strong>Result:</strong>
                <div className={`p-2 mt-1 rounded ${
                  toolCall.result?.success ? 'bg-success-subtle' : 'bg-danger-subtle'
                }`}>
                  {typeof toolCall.result?.result === 'string' 
                    ? toolCall.result.result 
                    : JSON.stringify(toolCall.result?.result, null, 2)
                  }
                </div>
              </div>
            </div>
          </Accordion.Body>
        </Accordion.Item>
      </Accordion>
    );
  };

  return (
    <div className="message-list">
      {messages.map((message) => (
        <div
          key={message.id}
          className={`message ${message.role === 'user' ? 'user-message' : 'assistant-message'} ${
            message.isError ? 'error-message' : ''
          }`}
        >
          <div className="message-header">
            <div className="message-avatar">
              {message.role === 'user' ? '👤' : '🤖'}
            </div>
            <div className="message-meta">
              <span className="message-role">
                {message.role === 'user' ? 'You' : 'AI Assistant'}
              </span>
              <span className="message-time">
                {formatTimestamp(message.timestamp)}
              </span>
            </div>
            {message.note && (
              <Badge bg="info" className="ms-auto">
                {message.note}
              </Badge>
            )}
          </div>
          
          <div className="message-content">
            <div className="message-text">
              {message.content}
            </div>
            
            {message.toolCalls && message.toolCalls.length > 0 && (
              <div className="tool-calls-section">
                <div className="tool-calls-header">
                  <Badge bg="primary" className="me-2">
                    🔧 Tool Executions ({message.toolCalls.length})
                  </Badge>
                </div>
                {message.toolCalls.map((toolCall, index) => formatToolCall(toolCall, index))}
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};

export default MessageList;
