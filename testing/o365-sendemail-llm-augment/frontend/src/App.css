.App {
  text-align: center;
}

.email-form {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.form-group {
  margin-bottom: 1rem;
  text-align: left;
}

.form-group label {
  font-weight: bold;
  margin-bottom: 0.5rem;
  display: block;
}

.recipient-input {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.recipient-input input {
  flex: 1;
}

.add-recipient-btn {
  margin-top: 10px;
}

.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.alert {
  margin-top: 20px;
}

.navbar-brand {
  font-weight: bold;
}

.welcome-section {
  background-color: #f8f9fa;
  padding: 40px 20px;
  border-radius: 10px;
  margin-bottom: 30px;
}

.feature-list {
  text-align: left;
  max-width: 600px;
  margin: 0 auto;
}

.feature-list li {
  margin-bottom: 10px;
}
