.App {
  text-align: center;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.email-form {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.form-group {
  margin-bottom: 1rem;
  text-align: left;
}

.form-group label {
  font-weight: bold;
  margin-bottom: 0.5rem;
  display: block;
}

.recipient-input {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.recipient-input input {
  flex: 1;
}

.add-recipient-btn {
  margin-top: 10px;
}

.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.alert {
  margin-top: 20px;
}

.navbar-brand {
  font-weight: bold;
}

.welcome-section {
  background-color: #f8f9fa;
  padding: 40px 20px;
  border-radius: 10px;
  margin-bottom: 30px;
}

.feature-list {
  text-align: left;
  max-width: 600px;
  margin: 0 auto;
}

.feature-list li {
  margin-bottom: 10px;
}

/* Chat Interface Styles */
.chat-interface {
  height: calc(100vh - 200px);
  padding: 0;
}

.chat-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-body {
  height: 100%;
  overflow: hidden;
}

.messages-container {
  overflow-y: auto;
  padding: 1rem;
  height: calc(100% - 200px);
}

.message-input-container {
  border-top: 1px solid #dee2e6;
  padding: 1rem;
  background-color: #f8f9fa;
}

/* Message Styles */
.message-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.message {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
}

.user-message {
  align-items: flex-end;
}

.assistant-message {
  align-items: flex-start;
}

.message-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  gap: 0.5rem;
}

.user-message .message-header {
  flex-direction: row-reverse;
}

.message-avatar {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #e9ecef;
}

.message-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  font-size: 0.8rem;
  color: #6c757d;
}

.user-message .message-meta {
  align-items: flex-end;
}

.message-content {
  max-width: 70%;
  padding: 1rem;
  border-radius: 1rem;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
}

.user-message .message-content {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.error-message .message-content {
  background-color: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.message-text {
  white-space: pre-wrap;
  word-wrap: break-word;
  text-align: left;
}

/* Tool Calls Styles */
.tool-calls-section {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #dee2e6;
}

.tool-calls-header {
  margin-bottom: 0.5rem;
}

.tool-call-details pre {
  font-size: 0.8rem;
  max-height: 200px;
  overflow-y: auto;
}

/* Message Input Styles */
.message-input {
  width: 100%;
}

.suggested-prompts {
  margin-bottom: 0.5rem;
}

.suggested-prompt-btn {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  margin: 0.1rem;
}

.message-textarea {
  border-radius: 1rem !important;
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.send-button {
  border-radius: 1rem !important;
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
  min-width: 80px;
}

.input-help-text {
  text-align: left;
}

/* Responsive Design */
@media (max-width: 768px) {
  .message-content {
    max-width: 90%;
  }

  .suggested-prompts {
    display: none;
  }

  .chat-interface {
    height: calc(100vh - 150px);
  }
}
