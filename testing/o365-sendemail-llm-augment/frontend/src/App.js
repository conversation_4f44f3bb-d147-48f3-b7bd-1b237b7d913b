import React, { useState, useEffect } from 'react';
import { useMsal, useIsAuthenticated } from '@azure/msal-react';
import { Container, Navbar, <PERSON>v, <PERSON><PERSON>, Card, Alert } from 'react-bootstrap';
import EmailForm from './components/EmailForm';
import LoginButton from './components/LoginButton';
import DebugInfo from './components/DebugInfo';
import { apiConfig } from './config';
import './App.css';

function App() {
  const { instance, accounts } = useMsal();
  const isAuthenticated = useIsAuthenticated();
  const [hasApiConsent, setHasApiConsent] = useState(false);
  const [consentLoading, setConsentLoading] = useState(false);
  const [consentMessage, setConsentMessage] = useState({ type: '', text: '' });

  const handleLogout = () => {
    instance.logoutPopup({
      postLogoutRedirectUri: window.location.origin,
      mainWindowRedirectUri: window.location.origin
    });
  };

  const getUserName = () => {
    if (accounts && accounts.length > 0) {
      return accounts[0].name || accounts[0].username;
    }
    return 'User';
  };

  // Check if user has already consented to API access
  useEffect(() => {
    if (isAuthenticated && accounts.length > 0) {
      checkApiConsent();
    }
  }, [isAuthenticated, accounts]);

  const checkApiConsent = async () => {
    try {
      const request = {
        scopes: apiConfig.scopes,
        account: accounts[0]
      };

      await instance.acquireTokenSilent(request);
      setHasApiConsent(true);
      setConsentMessage({ type: 'success', text: '✅ API permissions already granted' });
    } catch (error) {
      setHasApiConsent(false);
      setConsentMessage({ type: 'warning', text: '⚠️ API permissions required - click "Grant API Access" below' });
    }
  };

  const handleGrantConsent = async () => {
    setConsentLoading(true);
    setConsentMessage({ type: '', text: '' });

    try {
      const request = {
        scopes: apiConfig.scopes,
        account: accounts[0],
        prompt: 'consent',
        forceRefresh: true
      };

      const response = await instance.acquireTokenPopup(request);
      console.log('Consent granted successfully:', response);

      setHasApiConsent(true);
      setConsentMessage({ type: 'success', text: '✅ API permissions granted successfully! You can now send emails.' });
    } catch (error) {
      console.error('Consent failed:', error);
      setConsentMessage({
        type: 'danger',
        text: `❌ Consent failed: ${error.message || 'Please try again or contact your administrator.'}`
      });
    } finally {
      setConsentLoading(false);
    }
  };

  return (
    <div className="App">
      <Navbar bg="primary" variant="dark" expand="lg">
        <Container>
          <Navbar.Brand href="#home">
            📧 O365 Email Augment
          </Navbar.Brand>
          <Navbar.Toggle aria-controls="basic-navbar-nav" />
          <Navbar.Collapse id="basic-navbar-nav">
            <Nav className="me-auto">
              {isAuthenticated && (
                <Nav.Link href="#send-email">Send Email</Nav.Link>
              )}
            </Nav>
            <Nav>
              {isAuthenticated ? (
                <div className="d-flex align-items-center">
                  <span className="text-light me-3">
                    Welcome, {getUserName()}
                  </span>
                  <Button variant="outline-light" onClick={handleLogout}>
                    Logout
                  </Button>
                </div>
              ) : (
                <LoginButton />
              )}
            </Nav>
          </Navbar.Collapse>
        </Container>
      </Navbar>

      <Container className="mt-4">
        {isAuthenticated ? (
          <div>
            <div className="welcome-section">
              <h2>🎉 Successfully Authenticated!</h2>
              <p className="lead">
                You are now logged in with your Entra ID account.
              </p>
            </div>

            {/* API Consent Section */}
            <Card className="mb-4">
              <Card.Header>
                <h4>🔐 API Permissions</h4>
              </Card.Header>
              <Card.Body>
                <p>
                  To send emails on your behalf, this application needs permission to access the email API.
                </p>

                {consentMessage.text && (
                  <Alert variant={consentMessage.type} className="mb-3">
                    {consentMessage.text}
                  </Alert>
                )}

                {!hasApiConsent && (
                  <Button
                    variant="warning"
                    onClick={handleGrantConsent}
                    disabled={consentLoading}
                    size="lg"
                  >
                    {consentLoading ? (
                      <>
                        <span className="loading-spinner me-2"></span>
                        Requesting Permissions...
                      </>
                    ) : (
                      '🔓 Grant API Access'
                    )}
                  </Button>
                )}

                {hasApiConsent && (
                  <div className="text-success">
                    <strong>✅ API permissions granted - you can send emails below!</strong>
                  </div>
                )}
              </Card.Body>
            </Card>

            {/* Email Form - only show if consent is granted */}
            {hasApiConsent ? (
              <EmailForm />
            ) : (
              <Card>
                <Card.Body className="text-center text-muted">
                  <p>Please grant API permissions above to access the email form.</p>
                </Card.Body>
              </Card>
            )}

            {/* Debug Information */}
            <DebugInfo />
          </div>
        ) : (
          <div className="text-center">
            <div className="welcome-section">
              <h1>📧 O365 Email Augment</h1>
              <p className="lead">
                Send emails through Microsoft Graph API with Entra ID authentication
              </p>
              <p>
                Please sign in with your company Microsoft account to get started.
              </p>
              <LoginButton />
            </div>
            
            <Card className="mt-4">
              <Card.Header>
                <h4>Features</h4>
              </Card.Header>
              <Card.Body>
                <ul className="feature-list">
                  <li>🔐 Secure Entra ID authentication</li>
                  <li>📨 Send emails on behalf of authenticated users</li>
                  <li>🎯 Delegated permissions (no admin consent required)</li>
                  <li>🚀 Modern React SPA interface</li>
                  <li>⚡ FastAPI backend with automatic documentation</li>
                  <li>🐳 Docker containerization support</li>
                </ul>
              </Card.Body>
            </Card>
          </div>
        )}
      </Container>
    </div>
  );
}

export default App;
