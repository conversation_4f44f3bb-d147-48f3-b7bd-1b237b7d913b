import React, { useState, useEffect, useCallback } from 'react';
import { useMsal, useIsAuthenticated } from '@azure/msal-react';
import { Container, Navbar, Nav, <PERSON><PERSON>, Card, Alert, Tab, Tabs } from 'react-bootstrap';
import ChatInterface from './components/ChatInterface';
import EmailForm from './components/EmailForm';
import LoginButton from './components/LoginButton';
import DebugInfo from './components/DebugInfo';
import { apiConfig, graphScopesRequest } from './config';
import './App.css';

function App() {
  const { instance, accounts } = useMsal();
  const isAuthenticated = useIsAuthenticated();
  const [hasApiConsent, setHasApiConsent] = useState(false);
  const [hasGraphConsent, setHasGraphConsent] = useState(false);
  const [consentLoading, setConsentLoading] = useState(false);
  const [graphConsentLoading, setGraphConsentLoading] = useState(false);
  const [consentMessage, setConsentMessage] = useState({ type: '', text: '' });
  const [graphConsentMessage, setGraphConsentMessage] = useState({ type: '', text: '' });
  const [activeTab, setActiveTab] = useState('chat');

  const handleLogout = () => {
    instance.logoutPopup({
      postLogoutRedirectUri: window.location.origin,
      mainWindowRedirectUri: window.location.origin
    });
  };

  const getUserName = () => {
    if (accounts && accounts.length > 0) {
      return accounts[0].name || accounts[0].username;
    }
    return 'User';
  };

  const checkGraphConsent = useCallback(async () => {
    try {
      const request = {
        scopes: graphScopesRequest.scopes,
        account: accounts[0]
      };

      await instance.acquireTokenSilent(request);
      setHasGraphConsent(true);
      setGraphConsentMessage({ type: 'success', text: '✅ Microsoft Graph permissions already granted' });
    } catch (error) {
      setHasGraphConsent(false);
      setGraphConsentMessage({ type: 'warning', text: '⚠️ Microsoft Graph permissions required - click "Grant Graph Access" below' });
    }
  }, [instance, accounts]);

  const checkApiConsent = useCallback(async () => {
    try {
      const request = {
        scopes: apiConfig.scopes,
        account: accounts[0]
      };

      await instance.acquireTokenSilent(request);
      setHasApiConsent(true);
      setConsentMessage({ type: 'success', text: '✅ API permissions already granted' });
    } catch (error) {
      setHasApiConsent(false);
      setConsentMessage({ type: 'warning', text: '⚠️ API permissions required - click "Grant API Access" below' });
    }
  }, [instance, accounts]);

  // Check if user has already consented to API access
  useEffect(() => {
    if (isAuthenticated && accounts.length > 0) {
      checkGraphConsent();
      checkApiConsent();
    }
  }, [isAuthenticated, accounts, checkGraphConsent, checkApiConsent]);

  const handleGrantGraphConsent = async () => {
    setGraphConsentLoading(true);
    setGraphConsentMessage({ type: '', text: '' });

    try {
      const request = {
        scopes: graphScopesRequest.scopes,
        account: accounts[0],
        prompt: 'consent',
        forceRefresh: true
      };

      const response = await instance.acquireTokenPopup(request);
      console.log('Graph consent granted successfully:', response);

      setHasGraphConsent(true);
      setGraphConsentMessage({ type: 'success', text: '✅ Microsoft Graph permissions granted successfully!' });
    } catch (error) {
      console.error('Graph consent failed:', error);
      setGraphConsentMessage({
        type: 'danger',
        text: `❌ Graph consent failed: ${error.message || 'Please try again or contact your administrator.'}`
      });
    } finally {
      setGraphConsentLoading(false);
    }
  };

  const handleGrantConsent = async () => {
    setConsentLoading(true);
    setConsentMessage({ type: '', text: '' });

    try {
      const request = {
        scopes: apiConfig.scopes,
        account: accounts[0],
        prompt: 'consent',
        forceRefresh: true
      };

      const response = await instance.acquireTokenPopup(request);
      console.log('Consent granted successfully:', response);

      setHasApiConsent(true);
      setConsentMessage({ type: 'success', text: '✅ API permissions granted successfully! You can now use the AI assistant.' });
    } catch (error) {
      console.error('Consent failed:', error);
      setConsentMessage({
        type: 'danger',
        text: `❌ Consent failed: ${error.message || 'Please try again or contact your administrator.'}`
      });
    } finally {
      setConsentLoading(false);
    }
  };

  return (
    <div className="App">
      <Navbar bg="primary" variant="dark" expand="lg">
        <Container>
          <Navbar.Brand href="#home">
            🤖 AI Assistant with O365
          </Navbar.Brand>
          <Navbar.Toggle aria-controls="basic-navbar-nav" />
          <Navbar.Collapse id="basic-navbar-nav">
            <Nav className="me-auto">
              {isAuthenticated && (
                <>
                  <Nav.Link
                    href="#chat"
                    className={activeTab === 'chat' ? 'active' : ''}
                    onClick={() => setActiveTab('chat')}
                  >
                    💬 AI Chat
                  </Nav.Link>
                  <Nav.Link
                    href="#email"
                    className={activeTab === 'email' ? 'active' : ''}
                    onClick={() => setActiveTab('email')}
                  >
                    📧 Send Email
                  </Nav.Link>
                </>
              )}
            </Nav>
            <Nav>
              {isAuthenticated ? (
                <div className="d-flex align-items-center">
                  <span className="text-light me-3">
                    Welcome, {getUserName()}
                  </span>
                  <Button variant="outline-light" onClick={handleLogout}>
                    Logout
                  </Button>
                </div>
              ) : (
                <LoginButton />
              )}
            </Nav>
          </Navbar.Collapse>
        </Container>
      </Navbar>

      <Container className="mt-4">
        {isAuthenticated ? (
          <div>
            <div className="welcome-section">
              <h2>🎉 Successfully Authenticated!</h2>
              <p className="lead">
                You are now logged in with your Entra ID account and can access the AI assistant.
              </p>
            </div>

            {/* Microsoft Graph Consent Section */}
            <Card className="mb-4">
              <Card.Header>
                <h4>📊 Microsoft Graph Permissions (Step 1)</h4>
              </Card.Header>
              <Card.Body>
                <p>
                  First, grant permissions to access your Microsoft 365 data (email, calendar, contacts).
                </p>

                {graphConsentMessage.text && (
                  <Alert variant={graphConsentMessage.type} className="mb-3">
                    {graphConsentMessage.text}
                  </Alert>
                )}

                {!hasGraphConsent && (
                  <Button
                    variant="primary"
                    onClick={handleGrantGraphConsent}
                    disabled={graphConsentLoading}
                    size="lg"
                  >
                    {graphConsentLoading ? (
                      <>
                        <span className="loading-spinner me-2"></span>
                        Requesting Graph Permissions...
                      </>
                    ) : (
                      '🔓 Grant Graph Access'
                    )}
                  </Button>
                )}

                {hasGraphConsent && (
                  <div className="text-success">
                    <strong>✅ Microsoft Graph permissions granted!</strong>
                  </div>
                )}
              </Card.Body>
            </Card>

            {/* API Consent Section */}
            <Card className="mb-4">
              <Card.Header>
                <h4>🔐 API Permissions (Step 2)</h4>
              </Card.Header>
              <Card.Body>
                <p>
                  Next, grant permission for the AI assistant to access the backend API on your behalf.
                </p>

                {consentMessage.text && (
                  <Alert variant={consentMessage.type} className="mb-3">
                    {consentMessage.text}
                  </Alert>
                )}

                {!hasApiConsent && (
                  <Button
                    variant="warning"
                    onClick={handleGrantConsent}
                    disabled={consentLoading || !hasGraphConsent}
                    size="lg"
                  >
                    {consentLoading ? (
                      <>
                        <span className="loading-spinner me-2"></span>
                        Requesting API Permissions...
                      </>
                    ) : (
                      '🔓 Grant API Access'
                    )}
                  </Button>
                )}

                {!hasGraphConsent && !hasApiConsent && (
                  <div className="text-muted mt-2">
                    <small>Please complete Step 1 first</small>
                  </div>
                )}

                {hasApiConsent && (
                  <div className="text-success">
                    <strong>✅ API permissions granted - you can use the AI assistant below!</strong>
                  </div>
                )}
              </Card.Body>
            </Card>

            {/* Main Content - only show if both consents are granted */}
            {hasGraphConsent && hasApiConsent ? (
              <Tabs
                activeKey={activeTab}
                onSelect={(k) => setActiveTab(k)}
                className="mb-3"
              >
                <Tab eventKey="chat" title="💬 AI Chat Assistant">
                  <ChatInterface />
                </Tab>
                <Tab eventKey="email" title="📧 Direct Email">
                  <EmailForm />
                </Tab>
                <Tab eventKey="debug" title="🔧 Debug Info">
                  <DebugInfo />
                </Tab>
              </Tabs>
            ) : (
              <Card>
                <Card.Body className="text-center text-muted">
                  <p>Please complete both permission steps above to access the AI assistant and Microsoft 365 features.</p>
                </Card.Body>
              </Card>
            )}
          </div>
        ) : (
          <div className="text-center">
            <div className="welcome-section">
              <h1>🤖 AI Assistant with O365 Integration</h1>
              <p className="lead">
                Your intelligent assistant for Microsoft Outlook 365 with AI-powered chat interface
              </p>
              <p>
                Please sign in with your company Microsoft account to get started.
              </p>
              <LoginButton />
            </div>

            <Card className="mt-4">
              <Card.Header>
                <h4>Features</h4>
              </Card.Header>
              <Card.Body>
                <ul className="feature-list">
                  <li>🤖 AI-powered chat assistant with Azure OpenAI</li>
                  <li>📧 Smart email management and composition</li>
                  <li>📅 Calendar event creation and management</li>
                  <li>👥 Contact search and management</li>
                  <li>🔐 Secure Entra ID authentication</li>
                  <li>🎯 Delegated permissions (no admin consent required)</li>
                  <li>🚀 Modern React SPA interface</li>
                  <li>⚡ FastAPI backend with MCP integration</li>
                  <li>🔧 Tool-based AI interactions</li>
                </ul>
              </Card.Body>
            </Card>
          </div>
        )}
      </Container>
    </div>
  );
}

export default App;
