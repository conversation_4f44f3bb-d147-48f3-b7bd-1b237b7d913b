import React, { useState, useEffect, useCallback } from 'react';
import { useMsal, useIsAuthenticated } from '@azure/msal-react';
import { Container, Navbar, Nav, <PERSON><PERSON>, Card, Alert, Tab, Tabs } from 'react-bootstrap';
import ChatInterface from './components/ChatInterface';
import EmailForm from './components/EmailForm';
import LoginButton from './components/LoginButton';
import DebugInfo from './components/DebugInfo';
import { apiConfig } from './config';
import './App.css';

function App() {
  const { instance, accounts } = useMsal();
  const isAuthenticated = useIsAuthenticated();
  const [hasApiConsent, setHasApiConsent] = useState(false);
  const [consentLoading, setConsentLoading] = useState(false);
  const [consentMessage, setConsentMessage] = useState({ type: '', text: '' });
  const [activeTab, setActiveTab] = useState('chat');

  const handleLogout = () => {
    instance.logoutPopup({
      postLogoutRedirectUri: window.location.origin,
      mainWindowRedirectUri: window.location.origin
    });
  };

  const getUserName = () => {
    if (accounts && accounts.length > 0) {
      return accounts[0].name || accounts[0].username;
    }
    return 'User';
  };

  const checkApiConsent = useCallback(async () => {
    try {
      const request = {
        scopes: apiConfig.scopes,
        account: accounts[0]
      };

      await instance.acquireTokenSilent(request);
      setHasApiConsent(true);
      setConsentMessage({ type: 'success', text: '✅ API permissions already granted' });
    } catch (error) {
      setHasApiConsent(false);
      setConsentMessage({ type: 'warning', text: '⚠️ API permissions required - click "Grant API Access" below' });
    }
  }, [instance, accounts]);

  // Check if user has already consented to API access
  useEffect(() => {
    if (isAuthenticated && accounts.length > 0) {
      checkApiConsent();
    }
  }, [isAuthenticated, accounts, checkApiConsent]);

  const handleGrantConsent = async () => {
    setConsentLoading(true);
    setConsentMessage({ type: '', text: '' });

    try {
      const request = {
        scopes: apiConfig.scopes,
        account: accounts[0],
        prompt: 'consent',
        forceRefresh: true
      };

      const response = await instance.acquireTokenPopup(request);
      console.log('Consent granted successfully:', response);

      setHasApiConsent(true);
      setConsentMessage({ type: 'success', text: '✅ API permissions granted successfully! You can now send emails.' });
    } catch (error) {
      console.error('Consent failed:', error);
      setConsentMessage({
        type: 'danger',
        text: `❌ Consent failed: ${error.message || 'Please try again or contact your administrator.'}`
      });
    } finally {
      setConsentLoading(false);
    }
  };

  return (
    <div className="App">
      <Navbar bg="primary" variant="dark" expand="lg">
        <Container>
          <Navbar.Brand href="#home">
            🤖 AI Assistant with O365
          </Navbar.Brand>
          <Navbar.Toggle aria-controls="basic-navbar-nav" />
          <Navbar.Collapse id="basic-navbar-nav">
            <Nav className="me-auto">
              {isAuthenticated && (
                <>
                  <Nav.Link
                    href="#chat"
                    className={activeTab === 'chat' ? 'active' : ''}
                    onClick={() => setActiveTab('chat')}
                  >
                    💬 AI Chat
                  </Nav.Link>
                  <Nav.Link
                    href="#email"
                    className={activeTab === 'email' ? 'active' : ''}
                    onClick={() => setActiveTab('email')}
                  >
                    📧 Send Email
                  </Nav.Link>
                </>
              )}
            </Nav>
            <Nav>
              {isAuthenticated ? (
                <div className="d-flex align-items-center">
                  <span className="text-light me-3">
                    Welcome, {getUserName()}
                  </span>
                  <Button variant="outline-light" onClick={handleLogout}>
                    Logout
                  </Button>
                </div>
              ) : (
                <LoginButton />
              )}
            </Nav>
          </Navbar.Collapse>
        </Container>
      </Navbar>

      <Container className="mt-4">
        {isAuthenticated ? (
          <div>
            <div className="welcome-section">
              <h2>🎉 Successfully Authenticated!</h2>
              <p className="lead">
                You are now logged in with your Entra ID account and can access the AI assistant.
              </p>
            </div>

            {/* API Consent Section */}
            <Card className="mb-4">
              <Card.Header>
                <h4>🔐 API Permissions</h4>
              </Card.Header>
              <Card.Body>
                <p>
                  To use the AI assistant with Outlook 365 features, this application needs permission to access the Microsoft Graph API.
                </p>

                {consentMessage.text && (
                  <Alert variant={consentMessage.type} className="mb-3">
                    {consentMessage.text}
                  </Alert>
                )}

                {!hasApiConsent && (
                  <Button
                    variant="warning"
                    onClick={handleGrantConsent}
                    disabled={consentLoading}
                    size="lg"
                  >
                    {consentLoading ? (
                      <>
                        <span className="loading-spinner me-2"></span>
                        Requesting Permissions...
                      </>
                    ) : (
                      '🔓 Grant API Access'
                    )}
                  </Button>
                )}

                {hasApiConsent && (
                  <div className="text-success">
                    <strong>✅ API permissions granted - you can use the AI assistant below!</strong>
                  </div>
                )}
              </Card.Body>
            </Card>

            {/* Main Content - only show if consent is granted */}
            {hasApiConsent ? (
              <Tabs
                activeKey={activeTab}
                onSelect={(k) => setActiveTab(k)}
                className="mb-3"
              >
                <Tab eventKey="chat" title="💬 AI Chat Assistant">
                  <ChatInterface />
                </Tab>
                <Tab eventKey="email" title="📧 Direct Email">
                  <EmailForm />
                </Tab>
                <Tab eventKey="debug" title="🔧 Debug Info">
                  <DebugInfo />
                </Tab>
              </Tabs>
            ) : (
              <Card>
                <Card.Body className="text-center text-muted">
                  <p>Please grant API permissions above to access the AI assistant and email features.</p>
                </Card.Body>
              </Card>
            )}
          </div>
        ) : (
          <div className="text-center">
            <div className="welcome-section">
              <h1>🤖 AI Assistant with O365 Integration</h1>
              <p className="lead">
                Your intelligent assistant for Microsoft Outlook 365 with AI-powered chat interface
              </p>
              <p>
                Please sign in with your company Microsoft account to get started.
              </p>
              <LoginButton />
            </div>

            <Card className="mt-4">
              <Card.Header>
                <h4>Features</h4>
              </Card.Header>
              <Card.Body>
                <ul className="feature-list">
                  <li>🤖 AI-powered chat assistant with Azure OpenAI</li>
                  <li>📧 Smart email management and composition</li>
                  <li>📅 Calendar event creation and management</li>
                  <li>👥 Contact search and management</li>
                  <li>🔐 Secure Entra ID authentication</li>
                  <li>🎯 Delegated permissions (no admin consent required)</li>
                  <li>🚀 Modern React SPA interface</li>
                  <li>⚡ FastAPI backend with MCP integration</li>
                  <li>🔧 Tool-based AI interactions</li>
                </ul>
              </Card.Body>
            </Card>
          </div>
        )}
      </Container>
    </div>
  );
}

export default App;
