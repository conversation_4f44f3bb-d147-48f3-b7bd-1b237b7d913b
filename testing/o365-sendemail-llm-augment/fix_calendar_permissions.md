# Fix Calendar Permissions - 403 Access Denied

## 🚨 Problem
The AI Chat Assistant is getting "403 Forbidden" errors when trying to access calendar data because the Azure app registration is missing the required Microsoft Graph permissions.

## 🔧 Solution

### Step 1: Add Microsoft Graph Permissions to Backend App Registration

1. Go to [Azure Portal](https://portal.azure.com)
2. Navigate to **Azure Active Directory** > **App registrations**
3. Select your **backend API app registration**: `o365-email-api` (Client ID: `2c0cbcbe-7d89-448d-a88e-02453f272ee3`)
4. Go to **API permissions**
5. Click **Add a permission**
6. Select **Microsoft Graph** > **Delegated permissions**
7. Add these permissions:

```
✅ Calendars.Read - Read user calendars
✅ Calendars.ReadWrite - Read and write user calendars
✅ Mail.Read - Read user mail
✅ Mail.ReadWrite - Read and write user mail
✅ Mail.Send - Send mail as a user
✅ Contacts.Read - Read user contacts
✅ Contacts.ReadWrite - Read and write user contacts
✅ User.Read - Sign in and read user profile
```

8. Click **Add permissions** for each one

### Step 2: Grant Admin Consent (Recommended)

After adding the permissions:

1. In the **API permissions** section
2. Click **Grant admin consent for [Your Organization]**
3. Click **Yes** to confirm

This will grant the permissions for all users in your tenant.

### Step 3: Alternative - Use Consent URLs

If you prefer individual user consent, use these URLs:

**Admin Consent URL (Grants for all users):**
```
https://login.microsoftonline.com/42cc3295-cd0e-449c-b98e-5ce5b560c1d3/v2.0/adminconsent?client_id=2c0cbcbe-7d89-448d-a88e-02453f272ee3&scope=https%3A%2F%2Fgraph.microsoft.com%2F.default&redirect_uri=https%3A%2F%2Flocalhost
```

**User Consent URL (Individual users):**
```
https://login.microsoftonline.com/42cc3295-cd0e-449c-b98e-5ce5b560c1d3/oauth2/v2.0/authorize?client_id=6261b1c1-29e2-4e6c-99c2-fb285c7de4d0&response_type=token&redirect_uri=http%3A%2F%2Flocalhost%3A3000&scope=https%3A%2F%2Fgraph.microsoft.com%2FMail.Read+https%3A%2F%2Fgraph.microsoft.com%2FMail.ReadWrite+https%3A%2F%2Fgraph.microsoft.com%2FMail.Send+https%3A%2F%2Fgraph.microsoft.com%2FCalendars.Read+https%3A%2F%2Fgraph.microsoft.com%2FCalendars.ReadWrite+https%3A%2F%2Fgraph.microsoft.com%2FContacts.Read+https%3A%2F%2Fgraph.microsoft.com%2FContacts.ReadWrite+https%3A%2F%2Fgraph.microsoft.com%2FUser.Read&response_mode=fragment&state=consent_granted&nonce=consent_nonce&prompt=consent
```

## 🔍 Verification

After adding permissions, you can verify they're working:

### Check in Azure Portal
1. Go to **Azure Active Directory** > **App registrations**
2. Select your backend app registration
3. Go to **API permissions**
4. You should see all the Microsoft Graph permissions listed
5. They should show "Granted for [Your Organization]" if admin consent was given

### Test the Application
1. Restart your Docker containers:
   ```bash
   docker compose down
   docker compose up
   ```
2. Try the calendar command again: "What meetings do I have today?"
3. The 403 error should be resolved

## 🚨 Common Issues

### "Still getting 403 errors"
- Wait 5-10 minutes for permissions to propagate
- Clear browser cache and sign out/in again
- Restart the backend application

### "Permissions not showing up"
- Make sure you added permissions to the **backend** app registration (not frontend)
- Ensure you selected **Delegated permissions** (not Application permissions)
- Verify the correct Client ID: `2c0cbcbe-7d89-448d-a88e-02453f272ee3`

### "Admin consent failed"
- You need to be a tenant administrator
- Try the individual user consent URL instead
- Contact your IT administrator for help

## 📋 Required Permissions Summary

Your backend app registration should have these Microsoft Graph **Delegated** permissions:

| Permission | Purpose |
|------------|---------|
| `Calendars.Read` | Read user's calendar events |
| `Calendars.ReadWrite` | Create/modify calendar events |
| `Mail.Read` | Read user's emails |
| `Mail.ReadWrite` | Manage user's emails |
| `Mail.Send` | Send emails on behalf of user |
| `Contacts.Read` | Read user's contacts |
| `Contacts.ReadWrite` | Manage user's contacts |
| `User.Read` | Read user profile information |

## 🎯 Quick Fix Commands

Run these to get the consent URLs:

```bash
# Generate simple consent URLs
python simple_consent_urls.py

# Or serve the consent page
python serve_consent_page.py
# Then open: http://localhost:8080/simple_consent_page.html
```

After completing these steps, the calendar functionality should work perfectly! 🎉
