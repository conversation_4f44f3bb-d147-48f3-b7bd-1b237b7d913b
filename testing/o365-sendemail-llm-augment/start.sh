#!/bin/bash

# O365 Email Augment - Quick Start Script

echo "🚀 Starting O365 Email Augment Application"
echo "=========================================="

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ Error: .env file not found!"
    echo "Please copy .env.example to .env and fill in your Azure values."
    echo "See docs/AZURE_SETUP.md for detailed instructions."
    exit 1
fi

# Check if Docker is available
if command -v docker &> /dev/null && command -v docker-compose &> /dev/null; then
    echo "🐳 Docker detected. Starting with Docker Compose..."
    docker-compose up --build
else
    echo "📦 Docker not available. Starting manually..."
    
    # Start backend
    echo "Starting backend..."
    cd backend
    if [ ! -d "venv" ]; then
        echo "Creating Python virtual environment..."
        python3 -m venv venv
    fi
    source venv/bin/activate
    pip install -r requirements.txt
    python main.py &
    BACKEND_PID=$!
    cd ..
    
    # Start frontend
    echo "Starting frontend..."
    cd frontend
    if [ ! -d "node_modules" ]; then
        echo "Installing Node.js dependencies..."
        npm install
    fi
    npm start &
    FRONTEND_PID=$!
    cd ..
    
    echo "✅ Applications started!"
    echo "Frontend: http://localhost:3000"
    echo "Backend API: http://localhost:8000"
    echo "API Docs: http://localhost:8000/docs"
    echo ""
    echo "Press Ctrl+C to stop both applications"
    
    # Wait for Ctrl+C
    trap "echo 'Stopping applications...'; kill $BACKEND_PID $FRONTEND_PID; exit" INT
    wait
fi
