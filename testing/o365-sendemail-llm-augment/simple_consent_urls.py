#!/usr/bin/env python3
"""
Simple consent URL generator that avoids PKCE issues.
This creates URLs that work directly without complex PKCE handling.
"""

import os
import urllib.parse

def load_env_vars():
    """Load environment variables from .env file."""
    env_vars = {}
    try:
        with open('.env', 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key] = value
    except FileNotFoundError:
        print("Warning: .env file not found. Using environment variables.")
    
    # Fallback to environment variables
    for key in ['TENANT_ID', 'API_CLIENT_ID', 'REACT_APP_CLIENT_ID']:
        if key not in env_vars:
            env_vars[key] = os.environ.get(key, '')
    
    return env_vars

def generate_simple_consent_urls():
    """Generate simple consent URLs that work without PKCE issues."""
    
    env_vars = load_env_vars()
    tenant_id = env_vars.get('TENANT_ID')
    api_client_id = env_vars.get('API_CLIENT_ID')
    frontend_client_id = env_vars.get('REACT_APP_CLIENT_ID')
    
    if not all([tenant_id, api_client_id, frontend_client_id]):
        print("❌ Error: Missing required configuration.")
        return
    
    print("🔐 Simple Consent URLs (No PKCE Issues)")
    print("=" * 50)
    print()
    
    # Method 1: Admin consent for all permissions (recommended)
    admin_consent_url = f"https://login.microsoftonline.com/{tenant_id}/v2.0/adminconsent"
    admin_params = {
        "client_id": api_client_id,
        "scope": "https://graph.microsoft.com/.default",
        "redirect_uri": "https://localhost"
    }
    admin_url = f"{admin_consent_url}?{urllib.parse.urlencode(admin_params)}"
    
    print("🏢 ADMIN CONSENT URL (Recommended - No PKCE needed):")
    print("   This grants permissions for ALL users in your tenant.")
    print("   Only tenant admins can use this URL.")
    print(f"   {admin_url}")
    print()
    
    # Method 2: Direct permission grant URL (alternative)
    permissions = [
        "https://graph.microsoft.com/Mail.Read",
        "https://graph.microsoft.com/Mail.ReadWrite", 
        "https://graph.microsoft.com/Mail.Send",
        "https://graph.microsoft.com/Calendars.Read",
        "https://graph.microsoft.com/Calendars.ReadWrite",
        "https://graph.microsoft.com/Contacts.Read",
        "https://graph.microsoft.com/Contacts.ReadWrite",
        "https://graph.microsoft.com/User.Read"
    ]
    
    # Create a simple consent URL that redirects to the app
    simple_consent_url = f"https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/authorize"
    simple_params = {
        "client_id": frontend_client_id,
        "response_type": "token",  # Use implicit flow to avoid PKCE
        "redirect_uri": "http://localhost:3000",
        "scope": " ".join(permissions),
        "response_mode": "fragment",
        "state": "consent_granted",
        "nonce": "consent_nonce",
        "prompt": "consent"
    }
    simple_url = f"{simple_consent_url}?{urllib.parse.urlencode(simple_params)}"
    
    print("👤 SIMPLE USER CONSENT URL (Alternative):")
    print("   Uses implicit flow to avoid PKCE issues.")
    print("   Individual users can use this URL.")
    print(f"   {simple_url}")
    print()
    
    # Method 3: Azure Portal direct link
    portal_url = f"https://portal.azure.com/#view/Microsoft_AAD_RegisteredApps/ApplicationMenuBlade/~/CallAnAPI/appId/{api_client_id}/isMSAApp~/false"
    
    print("🌐 AZURE PORTAL DIRECT LINK:")
    print("   Direct link to grant permissions in Azure Portal.")
    print("   Users can manually grant permissions here.")
    print(f"   {portal_url}")
    print()
    
    print("📋 INSTRUCTIONS:")
    print()
    print("🏢 For Administrators (Recommended):")
    print("1. Use the ADMIN CONSENT URL")
    print("2. Sign in with admin credentials")
    print("3. Grant permissions for all users")
    print("4. All users can now use the app without individual consent")
    print()
    print("👤 For Individual Users:")
    print("1. Use the SIMPLE USER CONSENT URL")
    print("2. Sign in with user credentials") 
    print("3. Grant permissions for yourself")
    print("4. You can now use the AI chat features")
    print()
    print("🌐 Alternative (Azure Portal):")
    print("1. Use the AZURE PORTAL DIRECT LINK")
    print("2. Sign in and navigate to API permissions")
    print("3. Manually grant consent for required permissions")
    print()
    
    return {
        "admin_consent": admin_url,
        "user_consent": simple_url,
        "portal_link": portal_url
    }

if __name__ == "__main__":
    generate_simple_consent_urls()
