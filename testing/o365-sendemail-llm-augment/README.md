# O365 Email Augment - Entra ID Authenticated Email Sender

A modern SPA application with Python backend for sending emails through Microsoft Graph API using Entra ID authentication with delegated permissions.

## 🚀 Quick Start

1. **Setup Azure Entra ID**: Follow `docs/AZURE_SETUP.md`
2. **Configure Environment**: Copy `.env.example` to `.env` and fill in your Azure values
3. **Run with Docker**: `docker-compose up --build`
4. **Access Application**: Open http://localhost:3000

## Architecture

```
Frontend (React + MSAL) <-> Backend (FastAPI + MSAL) <-> Microsoft Graph API
```

- **Frontend**: React SPA with Microsoft Authentication Library (MSAL) for Entra ID authentication
- **Backend**: FastAPI server using On-Behalf-Of (OBO) flow to send emails via Graph API
- **Authentication**: Microsoft Entra ID with delegated permissions (no admin consent required)

## Features

- ✅ Entra ID authentication with company tenant
- ✅ Delegated permissions (user consent only)
- ✅ Email sending on behalf of authenticated user
- ✅ Modern React SPA interface
- ✅ FastAPI backend with automatic API documentation
- ✅ Docker containerization
- ✅ Comprehensive setup documentation

## Prerequisites

1. **Azure Entra ID Tenant**: Access to your company's Entra ID tenant
2. **User Permissions**: Ability to create app registrations in Entra ID
3. **Development Environment**: Node.js, Python 3.8+, Docker (optional)

## Project Structure

```
testing/o365-email-augment/
├── README.md
├── .env.example
├── docker-compose.yml
├── backend/
│   ├── main.py
│   ├── requirements.txt
│   ├── Dockerfile
│   └── .env
├── frontend/
│   ├── package.json
│   ├── public/
│   ├── src/
│   ├── Dockerfile
│   └── .env
└── docs/
    ├── AZURE_SETUP.md
    └── CONFIGURATION.md
```

## Environment Variables

### Backend (.env)
```env
TENANT_ID=your-tenant-id
API_CLIENT_ID=your-backend-api-client-id
API_CLIENT_SECRET=your-backend-api-client-secret
FRONTEND_URL=http://localhost:3000
```

### Frontend (.env)
```env
REACT_APP_CLIENT_ID=your-frontend-client-id
REACT_APP_TENANT_ID=your-tenant-id
REACT_APP_API_CLIENT_ID=your-backend-api-client-id
REACT_APP_REDIRECT_URI=http://localhost:3000
REACT_APP_API_BASE_URL=http://localhost:8000
```

## Development

### Backend
```bash
cd backend
pip install -r requirements.txt
python main.py
```

### Frontend
```bash
cd frontend
npm install
npm start
```

## Docker Deployment

```bash
# Build and run all services
docker-compose up --build

# Run in background
docker-compose up -d --build
```

## API Documentation

Once the backend is running, visit:
- API Documentation: http://localhost:8000/docs
- Health Check: http://localhost:8000/health

## Security

- Uses delegated permissions (no admin consent required)
- Secure token handling with MSAL
- CORS properly configured
- Environment variables for sensitive data
- HTTPS recommended for production

## Quick Start Commands

```bash
# 1. Setup Azure Entra ID (follow docs/AZURE_SETUP.md)
# 2. Configure environment variables
cp .env.example .env
# Edit .env with your Azure values

# 3. Start with Docker (recommended)
docker-compose up --build

# OR start manually
./start.sh

# OR start services individually
cd backend && python main.py
cd frontend && npm install && npm start
```

## API Endpoints

- `GET /` - API information
- `GET /health` - Health check
- `POST /send-email` - Send email on behalf of user
- `GET /docs` - Interactive API documentation

## License

MIT License
