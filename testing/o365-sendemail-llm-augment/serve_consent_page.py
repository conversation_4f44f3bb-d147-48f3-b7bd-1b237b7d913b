#!/usr/bin/env python3
"""
Simple HTTP server to serve the consent page.
This makes it easy to share the consent page with users.
"""

import http.server
import socketserver
import webbrowser
import os
from pathlib import Path

def serve_consent_page(port=8080):
    """Serve the consent page on the specified port."""
    
    # Change to the directory containing the HTML file
    os.chdir(Path(__file__).parent)
    
    # Create a simple HTTP server
    handler = http.server.SimpleHTTPRequestHandler
    
    try:
        with socketserver.TCPServer(("", port), handler) as httpd:
            print(f"🌐 Consent page server started!")
            print(f"📍 Local URL: http://localhost:{port}/consent_page.html")
            print(f"🔗 Share this URL with users who need to grant permissions")
            print(f"⏹️  Press Ctrl+C to stop the server")
            print()
            
            # Automatically open the page in the default browser
            webbrowser.open(f"http://localhost:{port}/consent_page.html")
            
            # Start serving
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"❌ Port {port} is already in use. Try a different port:")
            print(f"   python serve_consent_page.py --port 8081")
        else:
            print(f"❌ Error starting server: {e}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Serve the consent page for AI Chat Assistant")
    parser.add_argument("--port", type=int, default=8080, help="Port to serve on (default: 8080)")
    
    args = parser.parse_args()
    serve_consent_page(args.port)
