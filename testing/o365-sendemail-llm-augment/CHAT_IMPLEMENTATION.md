# AI Chat Assistant with Outlook 365 Integration

## Overview

This application has been enhanced to include an AI-powered chat assistant that integrates with Microsoft Outlook 365 through Azure OpenAI and Model Context Protocol (MCP). Users can now interact with their Outlook data using natural language through a modern chat interface.

## New Features

### 🤖 AI Chat Assistant
- **Azure OpenAI Integration**: Powered by GPT-4 for intelligent conversations
- **Natural Language Interface**: Ask questions and give commands in plain English
- **Tool-based Interactions**: AI can execute Outlook 365 operations on your behalf
- **Multi-turn Conversations**: Maintains context across multiple interactions

### 📧 Enhanced Email Management
- **Smart Email Composition**: AI can help draft and send emails
- **Inbox Management**: List, search, and organize emails through chat
- **Email Actions**: Reply, forward, and manage emails via AI commands

### 📅 Calendar Integration
- **Event Creation**: Create calendar events through natural language
- **Schedule Management**: View and manage your calendar through chat
- **Meeting Coordination**: AI can help schedule and organize meetings

### 👥 Contact Management
- **Contact Search**: Find contacts using natural language queries
- **Contact Information**: Retrieve contact details through chat
- **Smart Suggestions**: AI can suggest contacts based on context

## Architecture

### Frontend Changes
- **New Chat Interface**: Modern chat UI with message bubbles and tool execution display
- **Tabbed Navigation**: Switch between AI Chat, Direct Email, and Debug views
- **Real-time Updates**: Live chat with typing indicators and status updates
- **Responsive Design**: Works on desktop and mobile devices

### Backend Enhancements
- **Azure OpenAI Service**: Handles LLM interactions and tool orchestration
- **Embedded MCP Server**: Direct integration of Outlook 365 tools
- **Enhanced API**: New `/chat` endpoint for conversational interactions
- **Tool Execution**: Seamless integration between AI and Outlook operations

### MCP Integration
- **Embedded Tools**: Direct integration without external MCP server process
- **Graph API Client**: Optimized Microsoft Graph API interactions
- **Tool Definitions**: Comprehensive set of Outlook 365 operations
- **Error Handling**: Robust error handling and user feedback

## Usage Examples

### Email Operations
```
User: "Show me my recent emails"
AI: Lists recent emails with summaries

User: "Send an email to John about the meeting tomorrow"
AI: Composes and sends email with meeting details

User: "Find emails from Sarah about the project"
AI: Searches and displays relevant emails
```

### Calendar Management
```
User: "What meetings do I have today?"
AI: Shows today's calendar events

User: "Schedule a meeting with the team for next Tuesday at 2 PM"
AI: Creates calendar event with specified details

User: "Find a free time slot for a 1-hour meeting this week"
AI: Analyzes calendar and suggests available times
```

### Contact Operations
```
User: "Find contact information for Mike Johnson"
AI: Searches and displays contact details

User: "Show me all contacts from Acme Corp"
AI: Lists contacts from the specified company
```

## Configuration

### Environment Variables
Add these new variables to your `.env` file:

```bash
# Azure OpenAI Configuration
AZURE_OPENAI_API_KEY=your-azure-openai-api-key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4
AZURE_OPENAI_API_VERSION=2024-02-15-preview
```

### Azure OpenAI Setup
1. Create an Azure OpenAI resource in Azure Portal
2. Deploy a GPT-4 model
3. Get the API key and endpoint
4. Update environment variables

### Permissions
The application requires the same Microsoft Graph permissions as before:
- `Mail.Read`, `Mail.ReadWrite`, `Mail.Send`
- `Calendars.Read`, `Calendars.ReadWrite`
- `Contacts.Read`, `Contacts.ReadWrite`

## Technical Implementation

### New Dependencies
- `openai>=1.0.0` - Azure OpenAI client
- `mcp[cli]>=1.9.0` - Model Context Protocol
- `httpx>=0.27.0` - HTTP client for Graph API

### Key Components

#### 1. LLM Service (`llm_service.py`)
- Manages Azure OpenAI interactions
- Converts MCP tools to OpenAI function format
- Handles multi-turn conversations with tool execution

#### 2. MCP Client (`mcp_client.py`)
- Manages embedded MCP tools
- Provides interface between LLM and Outlook operations
- Handles authentication and error management

#### 3. MCP Server (`mcp_server.py`)
- Embedded Outlook 365 tools
- Direct Graph API integration
- Optimized for chat interactions

#### 4. Chat Interface (`ChatInterface.js`)
- Modern React chat component
- Real-time message handling
- Tool execution visualization

## API Endpoints

### New Chat Endpoint
```
POST /chat
Content-Type: application/json
Authorization: Bearer <access_token>

{
  "messages": [
    {"role": "user", "content": "Show me my recent emails"}
  ],
  "max_iterations": 5
}
```

Response:
```json
{
  "response": "Here are your recent emails...",
  "tool_calls": [
    {
      "tool": "list_messages",
      "arguments": {"folder_id": "inbox", "top": 10},
      "result": {"messages": [...], "success": true}
    }
  ],
  "success": true
}
```

## Development

### Running the Application
1. Install backend dependencies: `pip install -r backend/requirements.txt`
2. Install frontend dependencies: `npm install` (in frontend directory)
3. Configure environment variables
4. Start backend: `python backend/main.py`
5. Start frontend: `npm start` (in frontend directory)

### Testing
- Use the chat interface to test AI interactions
- Monitor backend logs for tool execution details
- Check browser console for frontend debugging

## Security Considerations

- **Token Management**: Access tokens are handled securely and not stored
- **Input Validation**: All user inputs are validated before processing
- **Error Handling**: Sensitive information is not exposed in error messages
- **Rate Limiting**: Consider implementing rate limiting for production use

## Future Enhancements

- **Voice Interface**: Add speech-to-text and text-to-speech
- **File Attachments**: Support for file uploads and attachments
- **Advanced Search**: Enhanced search capabilities across all data types
- **Workflow Automation**: Create automated workflows based on user patterns
- **Integration Expansion**: Add support for other Microsoft 365 services
