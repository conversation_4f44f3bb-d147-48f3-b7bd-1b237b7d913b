import os
import logging
from typing import List, Optional, Dict, Any
from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, EmailStr
import msal
import requests

# Import new services
from llm_service import get_llm_service, LLMServiceError
from mcp_client import get_mcp_manager

# Environment variables
TENANT_ID = os.environ.get("TENANT_ID")
API_CLIENT_ID = os.environ.get("API_CLIENT_ID")
API_CLIENT_SECRET = os.environ.get("API_CLIENT_SECRET")
FRONTEND_URL = os.environ.get("FRONTEND_URL", "http://localhost:3000")

# Microsoft Graph configuration
AUTHORITY = f"https://login.microsoftonline.com/{TENANT_ID}"
GRAPH_SCOPE = ["https://graph.microsoft.com/Mail.Send"]
GRAPH_ENDPOINT = "https://graph.microsoft.com/v1.0/me/sendMail"

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Validate required environment variables
if not all([TENANT_ID, API_CLIENT_ID, API_CLIENT_SECRET]):
    raise ValueError("Missing required environment variables: TENANT_ID, API_CLIENT_ID, API_CLIENT_SECRET")

# FastAPI app initialization
app = FastAPI(
    title="O365 Email Augment API",
    description="Send emails on behalf of authenticated users using Microsoft Graph API",
    version="1.0.0"
)

# CORS middleware configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=[FRONTEND_URL, "http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "OPTIONS"],
    allow_headers=["*"],
)

# Pydantic models for request/response validation
class EmailRecipient(BaseModel):
    email: EmailStr
    name: Optional[str] = None

class EmailRequest(BaseModel):
    to: List[EmailRecipient]
    subject: str
    body: str
    body_type: str = "Text"  # "Text" or "HTML"
    cc: Optional[List[EmailRecipient]] = None
    bcc: Optional[List[EmailRecipient]] = None

class EmailResponse(BaseModel):
    status: str
    message: str
    details: Optional[str] = None

class HealthResponse(BaseModel):
    status: str
    service: str
    version: str

# New models for chat functionality
class ChatMessage(BaseModel):
    role: str  # "user" or "assistant"
    content: str
    timestamp: Optional[str] = None

class ChatRequest(BaseModel):
    messages: List[ChatMessage]
    max_iterations: Optional[int] = 5

class ChatResponse(BaseModel):
    response: str
    tool_calls: List[Dict[str, Any]]
    success: bool
    note: Optional[str] = None

# Global MSAL client instance
msal_client = None

def get_msal_client():
    """Get or create MSAL confidential client application"""
    global msal_client
    if msal_client is None:
        try:
            msal_client = msal.ConfidentialClientApplication(
                client_id=API_CLIENT_ID,
                client_credential=API_CLIENT_SECRET,
                authority=AUTHORITY
            )
            logger.info("MSAL client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to create MSAL client: {e}")
            raise HTTPException(status_code=500, detail="Authentication service configuration error")
    return msal_client

def build_email_message(email_request: EmailRequest) -> dict:
    """Build Microsoft Graph email message from request"""
    # Build recipients
    to_recipients = [
        {"emailAddress": {"address": recipient.email, "name": recipient.name or recipient.email}}
        for recipient in email_request.to
    ]
    
    # Build email message
    message = {
        "subject": email_request.subject,
        "body": {
            "contentType": email_request.body_type,
            "content": email_request.body
        },
        "toRecipients": to_recipients
    }
    
    # Add CC recipients if provided
    if email_request.cc:
        message["ccRecipients"] = [
            {"emailAddress": {"address": recipient.email, "name": recipient.name or recipient.email}}
            for recipient in email_request.cc
        ]
    
    # Add BCC recipients if provided
    if email_request.bcc:
        message["bccRecipients"] = [
            {"emailAddress": {"address": recipient.email, "name": recipient.name or recipient.email}}
            for recipient in email_request.bcc
        ]
    
    return {
        "message": message,
        "saveToSentItems": "true"
    }

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    return HealthResponse(
        status="healthy",
        service="o365-email-augment-api",
        version="1.0.0"
    )

@app.post("/send-email", response_model=EmailResponse)
async def send_email(email_request: EmailRequest, request: Request):
    """
    Send email on behalf of authenticated user using Microsoft Graph API

    This endpoint uses the On-Behalf-Of (OBO) flow to acquire a token for Microsoft Graph
    and send an email on behalf of the authenticated user.
    """
    try:
        # Extract bearer token from Authorization header
        auth_header = request.headers.get("authorization")
        if not auth_header or not auth_header.lower().startswith("bearer "):
            logger.error("Missing or invalid authorization header")
            raise HTTPException(status_code=401, detail="Missing or invalid bearer token")

        user_token = auth_header.split()[1]
        logger.info(f"Attempting to send email with subject: '{email_request.subject}'")

        # Get MSAL client and perform On-Behalf-Of flow
        client = get_msal_client()
        result = client.acquire_token_on_behalf_of(
            user_assertion=user_token,
            scopes=GRAPH_SCOPE
        )

        # Check if token acquisition was successful
        if "access_token" not in result:
            error_msg = result.get('error', 'Unknown error')
            error_desc = result.get('error_description', 'No description available')
            logger.error(f"OBO flow failed: {error_msg} - {error_desc}")
            raise HTTPException(
                status_code=500,
                detail=f"Token acquisition failed: {error_msg}"
            )

        graph_token = result["access_token"]
        logger.info("Successfully acquired Graph API token via OBO flow")
        
        # Build email message
        mail_data = build_email_message(email_request)
        
        # Send email via Microsoft Graph API
        response = requests.post(
            GRAPH_ENDPOINT,
            headers={
                "Authorization": f"Bearer {graph_token}",
                "Content-Type": "application/json"
            },
            json=mail_data
        )
        
        # Check Graph API response
        if response.status_code >= 300:
            error_detail = response.text
            logger.error(f"Graph API error: {response.status_code} - {error_detail}")
            raise HTTPException(
                status_code=response.status_code,
                detail=f"Failed to send email: {error_detail}"
            )
        
        logger.info("Email sent successfully via Microsoft Graph API")
        return EmailResponse(
            status="success",
            message="Email sent successfully",
            details=f"Email with subject '{email_request.subject}' sent to {len(email_request.to)} recipient(s)"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in send_email: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.post("/chat", response_model=ChatResponse)
async def chat_completion(chat_request: ChatRequest, request: Request):
    """
    Process chat messages with LLM and MCP tool integration

    This endpoint processes user messages through Azure OpenAI and can execute
    Outlook 365 tools via the MCP server when needed.
    """
    try:
        # Extract bearer token from Authorization header
        auth_header = request.headers.get("authorization")
        if not auth_header or not auth_header.lower().startswith("bearer "):
            logger.error("Missing or invalid authorization header")
            raise HTTPException(status_code=401, detail="Missing or invalid bearer token")

        user_token = auth_header.split()[1]
        logger.info(f"Processing chat request with {len(chat_request.messages)} messages")

        # Get MSAL client and perform On-Behalf-Of flow
        client = get_msal_client()
        result = client.acquire_token_on_behalf_of(
            user_assertion=user_token,
            scopes=GRAPH_SCOPE
        )

        # Check if token acquisition was successful
        if "access_token" not in result:
            error_msg = result.get('error', 'Unknown error')
            error_desc = result.get('error_description', 'No description available')
            logger.error(f"OBO flow failed: {error_msg} - {error_desc}")
            raise HTTPException(
                status_code=500,
                detail=f"Token acquisition failed: {error_msg}"
            )

        graph_token = result["access_token"]
        logger.info("Successfully acquired Graph API token via OBO flow")

        # Convert chat messages to the format expected by LLM service
        messages = []
        for msg in chat_request.messages:
            messages.append({
                "role": msg.role,
                "content": msg.content
            })

        # Get LLM service and process the chat
        llm_service = get_llm_service()
        response = await llm_service.chat_completion(
            messages=messages,
            access_token=graph_token,
            max_iterations=chat_request.max_iterations
        )

        logger.info(f"Chat completion successful with {len(response.get('tool_calls', []))} tool calls")
        return ChatResponse(**response)

    except HTTPException:
        raise
    except LLMServiceError as e:
        logger.error(f"LLM service error: {e}")
        raise HTTPException(status_code=500, detail=f"LLM service error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error in chat completion: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "O365 Email Augment API with LLM Chat",
        "version": "2.0.0",
        "docs": "/docs",
        "health": "/health",
        "endpoints": {
            "chat": "/chat",
            "send_email": "/send-email"
        }
    }

@app.on_event("shutdown")
async def shutdown_event():
    """Clean up resources on shutdown."""
    try:
        mcp_manager = get_mcp_manager()
        await mcp_manager.cleanup()
        logger.info("MCP manager cleaned up successfully")
    except Exception as e:
        logger.error(f"Error during shutdown cleanup: {e}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
