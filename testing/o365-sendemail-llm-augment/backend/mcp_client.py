"""MCP Client implementation for integrating with Outlook 365 MCP server."""

import asyncio
import logging
import os
from typing import Any, Dict, List, Optional, Tuple
from contextlib import asynccontextmanager

from mcp_server import OutlookMCPTools, GraphAPIError

logger = logging.getLogger(__name__)


class MCPClientError(Exception):
    """Custom exception for MCP client errors."""
    pass


class OutlookMCPClient:
    """Client for communicating with the embedded Outlook 365 MCP tools."""

    def __init__(self, access_token: str):
        """
        Initialize the MCP client.

        Args:
            access_token: OAuth access token for Graph API
        """
        self.access_token = access_token
        self.tools: Optional[OutlookMCPTools] = None
        
    async def __aenter__(self):
        """Async context manager entry."""
        await self.connect()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.disconnect()
        
    async def connect(self):
        """Connect to the embedded MCP tools."""
        try:
            # Initialize the embedded tools
            self.tools = OutlookMCPTools(self.access_token)
            logger.info("Successfully initialized embedded MCP tools")

        except Exception as e:
            logger.error(f"Failed to initialize MCP tools: {e}")
            raise MCPClientError(f"Initialization failed: {e}")
    
    async def disconnect(self):
        """Disconnect from the embedded MCP tools."""
        try:
            self.tools = None
            logger.info("Disconnected from embedded MCP tools")

        except Exception as e:
            logger.error(f"Error during disconnect: {e}")
    
    async def list_tools(self) -> List[Dict[str, Any]]:
        """List available tools from the embedded MCP tools."""
        if not self.tools:
            raise MCPClientError("Not connected to MCP tools")

        # Return a list of available tools
        return [
            {
                "name": "list_messages",
                "description": "List messages in a mail folder",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "folder_id": {"type": "string", "default": "inbox"},
                        "select": {"type": "string"},
                        "filter_query": {"type": "string"},
                        "order_by": {"type": "string"},
                        "top": {"type": "integer", "default": 50}
                    }
                }
            },
            {
                "name": "send_message",
                "description": "Send an email message",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "subject": {"type": "string", "description": "Email subject"},
                        "to": {"type": "string", "description": "Recipient email address"},
                        "to_recipients": {"type": "array", "items": {"type": "string"}, "description": "List of recipient email addresses"},
                        "body": {"type": "string", "description": "Email body content"},
                        "content": {"type": "string", "description": "Email content"},
                        "message_data": {"type": "object", "description": "Complete message object"}
                    },
                    "required": []
                }
            },
            {
                "name": "list_events",
                "description": "List events in a calendar. Can filter by date range.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "calendar_id": {"type": "string", "default": "primary", "description": "Calendar ID or 'primary'"},
                        "select": {"type": "string", "description": "Fields to select"},
                        "filter_query": {"type": "string", "description": "OData filter query"},
                        "order_by": {"type": "string", "description": "Order by clause"},
                        "top": {"type": "integer", "default": 50, "description": "Number of events to return"},
                        "start_date": {"type": "string", "description": "Start date for filtering (YYYY-MM-DD or ISO format)"},
                        "end_date": {"type": "string", "description": "End date for filtering (YYYY-MM-DD or ISO format)"}
                    }
                }
            },
            {
                "name": "create_event",
                "description": "Create a new calendar event",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "subject": {"type": "string", "description": "Event subject/title"},
                        "title": {"type": "string", "description": "Event title (alternative to subject)"},
                        "body": {"type": "string", "description": "Event description/body"},
                        "description": {"type": "string", "description": "Event description (alternative to body)"},
                        "start": {"type": "string", "description": "Start date/time (ISO format or YYYY-MM-DD)"},
                        "start_time": {"type": "string", "description": "Start time (alternative to start)"},
                        "start_date": {"type": "string", "description": "Start date (alternative to start)"},
                        "end": {"type": "string", "description": "End date/time (ISO format or YYYY-MM-DD)"},
                        "end_time": {"type": "string", "description": "End time (alternative to end)"},
                        "end_date": {"type": "string", "description": "End date (alternative to end)"},
                        "location": {"type": "string", "description": "Event location"},
                        "attendees": {"type": "array", "items": {"type": "string"}, "description": "List of attendee email addresses"},
                        "calendar_id": {"type": "string", "default": "primary", "description": "Calendar ID"},
                        "event_data": {"type": "object", "description": "Complete event object (alternative to individual fields)"}
                    },
                    "required": []
                }
            },
            {
                "name": "list_contacts",
                "description": "List contacts",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "select": {"type": "string"},
                        "filter_query": {"type": "string"},
                        "order_by": {"type": "string"},
                        "top": {"type": "integer", "default": 50}
                    }
                }
            },
            {
                "name": "search_contacts",
                "description": "Search contacts by query",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string"},
                        "top": {"type": "integer", "default": 50}
                    },
                    "required": ["query"]
                }
            },
            {
                "name": "get_todays_events",
                "description": "Get today's calendar events (simpler than list_events for today)",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "calendar_id": {"type": "string", "default": "primary", "description": "Calendar ID or 'primary'"}
                    }
                }
            },
            {
                "name": "get_user_profile",
                "description": "Get user profile information",
                "parameters": {
                    "type": "object",
                    "properties": {}
                }
            }
        ]
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Call a tool on the embedded MCP tools.

        Args:
            tool_name: Name of the tool to call
            arguments: Arguments to pass to the tool

        Returns:
            Tool execution result
        """
        if not self.tools:
            raise MCPClientError("Not connected to MCP tools")

        try:
            # Call the appropriate tool method
            if tool_name == "list_messages":
                result = await self.tools.list_messages(**arguments)
            elif tool_name == "send_message":
                result = await self.tools.send_message(**arguments)
            elif tool_name == "list_events":
                result = await self.tools.list_events(**arguments)
            elif tool_name == "create_event":
                result = await self.tools.create_event(**arguments)
            elif tool_name == "list_contacts":
                result = await self.tools.list_contacts(**arguments)
            elif tool_name == "search_contacts":
                result = await self.tools.search_contacts(**arguments)
            elif tool_name == "get_todays_events":
                result = await self.tools.get_todays_events(**arguments)
            elif tool_name == "get_user_profile":
                result = await self.tools.get_user_profile()
            else:
                raise MCPClientError(f"Unknown tool: {tool_name}")

            return result

        except GraphAPIError as e:
            logger.error(f"Graph API error calling tool {tool_name}: {e}")
            raise MCPClientError(f"Graph API error calling tool {tool_name}: {e}")
        except Exception as e:
            logger.error(f"Failed to call tool {tool_name}: {e}")
            raise MCPClientError(f"Failed to call tool {tool_name}: {e}")
    
class MCPClientManager:
    """Manager for MCP client instances."""

    def __init__(self):
        """Initialize the MCP client manager."""
        self._clients: Dict[str, OutlookMCPClient] = {}
    
    @asynccontextmanager
    async def get_client(self, access_token: str):
        """
        Get or create an MCP client for the given access token.

        Args:
            access_token: OAuth access token for Graph API

        Yields:
            OutlookMCPClient instance
        """
        client_key = access_token[:10]  # Use first 10 chars as key

        if client_key in self._clients:
            yield self._clients[client_key]
        else:
            client = OutlookMCPClient(access_token)
            try:
                await client.connect()
                self._clients[client_key] = client
                yield client
            finally:
                # Keep client connected for reuse
                pass
    
    async def cleanup(self):
        """Clean up all client connections."""
        for client in self._clients.values():
            await client.disconnect()
        self._clients.clear()


# Global MCP client manager instance
mcp_manager: Optional[MCPClientManager] = None


def get_mcp_manager() -> MCPClientManager:
    """Get the global MCP client manager instance."""
    global mcp_manager
    if mcp_manager is None:
        mcp_manager = MCPClientManager()
    return mcp_manager
