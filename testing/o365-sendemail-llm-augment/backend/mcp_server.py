"""Embedded MCP Server for Outlook 365 integration."""

import logging
import os
from typing import Any, Dict, List, Optional
import httpx

logger = logging.getLogger(__name__)

# Microsoft Graph API Configuration
GRAPH_BASE_URL = "https://graph.microsoft.com/v1.0"

# Default headers for Graph API requests
DEFAULT_HEADERS = {
    "Content-Type": "application/json",
    "Accept": "application/json"
}

class GraphAPIError(Exception):
    """Custom exception for Graph API errors."""
    
    def __init__(self, message: str, status_code: int = None, response_data: Dict = None):
        super().__init__(message)
        self.status_code = status_code
        self.response_data = response_data


class GraphClient:
    """Microsoft Graph API client with access token support."""
    
    def __init__(self, access_token: str):
        """Initialize Graph client with access token."""
        self.access_token = access_token
        self.base_url = GRAPH_BASE_URL
        self.headers = {
            **DEFAULT_HEADERS,
            "Authorization": f"Bearer {access_token}"
        }
    
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """Make HTTP request to Microsoft Graph API."""
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        request_headers = {**self.headers}
        if headers:
            request_headers.update(headers)
        
        logger.info(f"Making {method} request to {url}")
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.request(
                    method=method,
                    url=url,
                    params=params,
                    json=data,
                    headers=request_headers,
                    timeout=30.0
                )
                
                logger.info(f"Response status: {response.status_code}")
                
                if response.status_code >= 400:
                    error_data = {}
                    try:
                        error_data = response.json()
                    except:
                        pass
                    
                    error_msg = f"Graph API error: {response.status_code}"
                    if error_data.get("error", {}).get("message"):
                        error_msg += f" - {error_data['error']['message']}"
                    
                    raise GraphAPIError(
                        message=error_msg,
                        status_code=response.status_code,
                        response_data=error_data
                    )
                
                # Handle empty responses (e.g., DELETE operations)
                if response.status_code == 204 or not response.content:
                    return {"success": True}
                
                return response.json()
                
            except httpx.RequestError as e:
                logger.error(f"Request error: {e}")
                raise GraphAPIError(f"Request failed: {str(e)}")
    
    async def get(self, endpoint: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Make GET request."""
        return await self._make_request("GET", endpoint, params=params)
    
    async def post(self, endpoint: str, data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Make POST request."""
        return await self._make_request("POST", endpoint, data=data)
    
    async def patch(self, endpoint: str, data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Make PATCH request."""
        return await self._make_request("PATCH", endpoint, data=data)
    
    async def delete(self, endpoint: str) -> Dict[str, Any]:
        """Make DELETE request."""
        return await self._make_request("DELETE", endpoint)


class OutlookMCPTools:
    """Embedded MCP tools for Outlook 365 operations."""
    
    def __init__(self, access_token: str):
        """Initialize with access token."""
        self.client = GraphClient(access_token)
    
    async def list_messages(
        self,
        folder_id: str = "inbox",
        select: Optional[str] = None,
        filter_query: Optional[str] = None,
        order_by: Optional[str] = None,
        top: int = 50
    ) -> Dict[str, Any]:
        """List messages in a mail folder."""
        try:
            params = {"$top": min(top, 999)}
            
            if select:
                params["$select"] = select
            if filter_query:
                params["$filter"] = filter_query
            if order_by:
                params["$orderby"] = order_by
            
            endpoint = f"me/mailFolders/{folder_id}/messages"
            result = await self.client.get(endpoint, params)
            
            return {
                "messages": result.get("value", []),
                "count": len(result.get("value", [])),
                "folder": folder_id
            }
            
        except GraphAPIError as e:
            logger.error(f"Failed to list messages: {e}")
            raise
    
    async def send_message(self, message_data: Dict[str, Any]) -> Dict[str, Any]:
        """Send an email message."""
        try:
            # Prepare the message payload
            payload = {
                "message": message_data,
                "saveToSentItems": "true"
            }
            
            result = await self.client.post("me/sendMail", payload)
            
            return {
                "success": True,
                "message": "Email sent successfully",
                "result": result
            }
            
        except GraphAPIError as e:
            logger.error(f"Failed to send message: {e}")
            raise
    
    async def list_events(
        self,
        calendar_id: str = "primary",
        select: Optional[str] = None,
        filter_query: Optional[str] = None,
        order_by: Optional[str] = None,
        top: int = 50
    ) -> Dict[str, Any]:
        """List events in a calendar."""
        try:
            params = {"$top": min(top, 999)}
            
            if select:
                params["$select"] = select
            if filter_query:
                params["$filter"] = filter_query
            if order_by:
                params["$orderby"] = order_by
            
            if calendar_id == "primary":
                endpoint = "me/events"
            else:
                endpoint = f"me/calendars/{calendar_id}/events"
            
            result = await self.client.get(endpoint, params)
            
            return {
                "events": result.get("value", []),
                "count": len(result.get("value", [])),
                "calendar": calendar_id
            }
            
        except GraphAPIError as e:
            logger.error(f"Failed to list events: {e}")
            raise
    
    async def create_event(self, event_data: Dict[str, Any], calendar_id: str = "primary") -> Dict[str, Any]:
        """Create a new calendar event."""
        try:
            if calendar_id == "primary":
                endpoint = "me/events"
            else:
                endpoint = f"me/calendars/{calendar_id}/events"
            
            result = await self.client.post(endpoint, event_data)
            
            return {
                "success": True,
                "message": "Event created successfully",
                "event": result
            }
            
        except GraphAPIError as e:
            logger.error(f"Failed to create event: {e}")
            raise
    
    async def list_contacts(
        self,
        select: Optional[str] = None,
        filter_query: Optional[str] = None,
        order_by: Optional[str] = None,
        top: int = 50
    ) -> Dict[str, Any]:
        """List contacts."""
        try:
            params = {"$top": min(top, 999)}
            
            if select:
                params["$select"] = select
            if filter_query:
                params["$filter"] = filter_query
            if order_by:
                params["$orderby"] = order_by
            
            result = await self.client.get("me/contacts", params)
            
            return {
                "contacts": result.get("value", []),
                "count": len(result.get("value", []))
            }
            
        except GraphAPIError as e:
            logger.error(f"Failed to list contacts: {e}")
            raise
    
    async def search_contacts(self, query: str, top: int = 50) -> Dict[str, Any]:
        """Search contacts by query."""
        try:
            params = {
                "$search": f'"{query}"',
                "$top": min(top, 999)
            }
            
            result = await self.client.get("me/contacts", params)
            
            return {
                "contacts": result.get("value", []),
                "count": len(result.get("value", [])),
                "query": query
            }
            
        except GraphAPIError as e:
            logger.error(f"Failed to search contacts: {e}")
            raise
    
    async def get_user_profile(self) -> Dict[str, Any]:
        """Get user profile information."""
        try:
            result = await self.client.get("me")
            
            return {
                "profile": result,
                "success": True
            }
            
        except GraphAPIError as e:
            logger.error(f"Failed to get user profile: {e}")
            raise
