"""Azure OpenAI service for LLM integration with MCP tools."""

import json
import logging
import os
from typing import Any, Dict, List, Optional, Union

from openai import AsyncAzureOpenAI
import mcp.types as types

from mcp_client import get_mcp_manager, MCPClientError

logger = logging.getLogger(__name__)


class LLMServiceError(Exception):
    """Custom exception for LLM service errors."""
    pass


class AzureOpenAIService:
    """Service for interacting with Azure OpenAI with MCP tool integration."""
    
    def __init__(self):
        """Initialize the Azure OpenAI service."""
        # Get configuration from environment variables
        self.api_key = os.environ.get("AZURE_OPENAI_API_KEY")
        self.endpoint = os.environ.get("AZURE_OPENAI_ENDPOINT")
        self.deployment_name = os.environ.get("AZURE_OPENAI_DEPLOYMENT_NAME", "gpt-4")
        self.api_version = os.environ.get("AZURE_OPENAI_API_VERSION", "2024-02-15-preview")
        
        if not all([self.api_key, self.endpoint]):
            raise LLMServiceError(
                "Missing required Azure OpenAI configuration. "
                "Please set AZURE_OPENAI_API_KEY and AZURE_OPENAI_ENDPOINT environment variables."
            )
        
        # Initialize Azure OpenAI client
        self.client = AsyncAzureOpenAI(
            api_key=self.api_key,
            azure_endpoint=self.endpoint,
            api_version=self.api_version
        )
        
        self.mcp_manager = get_mcp_manager()
    
    async def get_available_tools(self, access_token: str) -> List[Dict[str, Any]]:
        """
        Get available MCP tools and convert them to OpenAI function format.
        
        Args:
            access_token: OAuth access token for MCP server
            
        Returns:
            List of tools in OpenAI function format
        """
        try:
            async with self.mcp_manager.get_client(access_token) as mcp_client:
                mcp_tools = await mcp_client.list_tools()

                # Convert MCP tools to OpenAI function format
                openai_tools = []
                for tool in mcp_tools:
                    openai_tool = {
                        "type": "function",
                        "function": {
                            "name": tool["name"],
                            "description": tool["description"],
                            "parameters": tool.get("parameters", {
                                "type": "object",
                                "properties": {},
                                "required": []
                            })
                        }
                    }

                    openai_tools.append(openai_tool)

                return openai_tools
                
        except MCPClientError as e:
            logger.error(f"Failed to get MCP tools: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error getting tools: {e}")
            return []
    
    async def execute_tool(self, access_token: str, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute an MCP tool.
        
        Args:
            access_token: OAuth access token for MCP server
            tool_name: Name of the tool to execute
            arguments: Tool arguments
            
        Returns:
            Tool execution result
        """
        try:
            async with self.mcp_manager.get_client(access_token) as mcp_client:
                result = await mcp_client.call_tool(tool_name, arguments)

                # The result is already in a standard format from our embedded tools
                return {"result": result, "success": True}
                
        except MCPClientError as e:
            logger.error(f"Failed to execute tool {tool_name}: {e}")
            return {"result": f"Tool execution failed: {e}", "success": False}
        except Exception as e:
            logger.error(f"Unexpected error executing tool {tool_name}: {e}")
            return {"result": f"Unexpected error: {e}", "success": False}
    
    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        access_token: str,
        max_iterations: int = 5
    ) -> Dict[str, Any]:
        """
        Process a chat completion with MCP tool integration.
        
        Args:
            messages: List of chat messages
            access_token: OAuth access token for MCP server
            max_iterations: Maximum number of tool call iterations
            
        Returns:
            Chat completion response with tool execution results
        """
        try:
            # Get available tools
            tools = await self.get_available_tools(access_token)
            
            # Prepare messages for OpenAI
            openai_messages = []
            for msg in messages:
                openai_messages.append({
                    "role": msg["role"],
                    "content": msg["content"]
                })
            
            # Add system message about available tools
            if tools:
                tool_descriptions = []
                for tool in tools:
                    tool_descriptions.append(f"- {tool['function']['name']}: {tool['function']['description']}")
                
                system_message = {
                    "role": "system",
                    "content": (
                        "You are an AI assistant with access to Microsoft Outlook 365 tools. "
                        "You can help users with email, calendar, and contacts management. "
                        f"Available tools:\n" + "\n".join(tool_descriptions) + "\n\n"
                        "Use these tools when appropriate to help the user accomplish their tasks."
                    )
                }
                openai_messages.insert(0, system_message)
            
            iteration = 0
            tool_call_history = []
            
            while iteration < max_iterations:
                # Make the API call
                response = await self.client.chat.completions.create(
                    model=self.deployment_name,
                    messages=openai_messages,
                    tools=tools if tools else None,
                    tool_choice="auto" if tools else None,
                    temperature=0.7,
                    max_tokens=1500
                )
                
                message = response.choices[0].message
                
                # Check if the model wants to call tools
                if message.tool_calls:
                    # Add the assistant's message to the conversation
                    openai_messages.append({
                        "role": "assistant",
                        "content": message.content,
                        "tool_calls": [
                            {
                                "id": tc.id,
                                "type": tc.type,
                                "function": {
                                    "name": tc.function.name,
                                    "arguments": tc.function.arguments
                                }
                            }
                            for tc in message.tool_calls
                        ]
                    })
                    
                    # Execute each tool call
                    for tool_call in message.tool_calls:
                        try:
                            # Parse arguments
                            arguments = json.loads(tool_call.function.arguments)
                            
                            # Execute the tool
                            tool_result = await self.execute_tool(
                                access_token,
                                tool_call.function.name,
                                arguments
                            )
                            
                            # Add tool result to conversation
                            openai_messages.append({
                                "role": "tool",
                                "tool_call_id": tool_call.id,
                                "content": json.dumps(tool_result)
                            })
                            
                            # Track tool calls for response
                            tool_call_history.append({
                                "tool": tool_call.function.name,
                                "arguments": arguments,
                                "result": tool_result
                            })
                            
                        except json.JSONDecodeError as e:
                            logger.error(f"Failed to parse tool arguments: {e}")
                            openai_messages.append({
                                "role": "tool",
                                "tool_call_id": tool_call.id,
                                "content": json.dumps({"result": f"Invalid arguments: {e}", "success": False})
                            })
                    
                    iteration += 1
                else:
                    # No more tool calls, return the final response
                    return {
                        "response": message.content,
                        "tool_calls": tool_call_history,
                        "success": True
                    }
            
            # If we've reached max iterations, return what we have
            return {
                "response": "I've completed the available tool executions. Is there anything else you'd like me to help with?",
                "tool_calls": tool_call_history,
                "success": True,
                "note": f"Reached maximum iterations ({max_iterations})"
            }
            
        except Exception as e:
            logger.error(f"Chat completion failed: {e}")
            return {
                "response": f"I encountered an error while processing your request: {str(e)}",
                "tool_calls": [],
                "success": False
            }


# Global service instance
llm_service: Optional[AzureOpenAIService] = None


def get_llm_service() -> AzureOpenAIService:
    """Get the global LLM service instance."""
    global llm_service
    if llm_service is None:
        llm_service = AzureOpenAIService()
    return llm_service
