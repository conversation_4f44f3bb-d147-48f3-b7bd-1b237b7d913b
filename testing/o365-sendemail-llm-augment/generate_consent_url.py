#!/usr/bin/env python3
"""
Generate Microsoft Entra ID consent URLs for the AI Chat Assistant application.

This script generates URLs that end users can open to grant all necessary permissions
for the application to work with Outlook 365 features.
"""

import os
import urllib.parse
from typing import List, Dict

def load_env_vars():
    """Load environment variables from .env file."""
    env_vars = {}
    try:
        with open('.env', 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key] = value
    except FileNotFoundError:
        print("Warning: .env file not found. Using environment variables.")
    
    # Fallback to environment variables
    for key in ['TENANT_ID', 'API_CLIENT_ID', 'REACT_APP_CLIENT_ID']:
        if key not in env_vars:
            env_vars[key] = os.environ.get(key, '')
    
    return env_vars

def get_required_permissions() -> List[Dict[str, str]]:
    """Get the list of required Microsoft Graph permissions."""
    return [
        # Email permissions
        {
            "permission": "Mail.Read",
            "type": "Delegated",
            "description": "Read user's email messages"
        },
        {
            "permission": "Mail.ReadWrite", 
            "type": "Delegated",
            "description": "Read and write user's email messages"
        },
        {
            "permission": "Mail.Send",
            "type": "Delegated", 
            "description": "Send email on behalf of the user"
        },
        
        # Calendar permissions
        {
            "permission": "Calendars.Read",
            "type": "Delegated",
            "description": "Read user's calendar events"
        },
        {
            "permission": "Calendars.ReadWrite",
            "type": "Delegated",
            "description": "Read and write user's calendar events"
        },
        
        # Contact permissions
        {
            "permission": "Contacts.Read",
            "type": "Delegated",
            "description": "Read user's contacts"
        },
        {
            "permission": "Contacts.ReadWrite",
            "type": "Delegated",
            "description": "Read and write user's contacts"
        },
        
        # User profile permissions
        {
            "permission": "User.Read",
            "type": "Delegated",
            "description": "Read user's profile information"
        }
    ]

def generate_admin_consent_url(tenant_id: str, client_id: str) -> str:
    """Generate admin consent URL for tenant-wide permissions."""
    base_url = "https://login.microsoftonline.com"
    
    params = {
        "client_id": client_id,
        "response_type": "code",
        "redirect_uri": "https://localhost",  # Dummy redirect for admin consent
        "response_mode": "query",
        "scope": "https://graph.microsoft.com/.default",
        "state": "admin_consent"
    }
    
    query_string = urllib.parse.urlencode(params)
    return f"{base_url}/{tenant_id}/v2.0/adminconsent?{query_string}"

def generate_user_consent_url(tenant_id: str, client_id: str, redirect_uri: str = "http://localhost:3000") -> str:
    """Generate user consent URL for individual user permissions."""
    base_url = "https://login.microsoftonline.com"
    
    # Build scope string with all required permissions
    permissions = get_required_permissions()
    scopes = ["openid", "profile", "offline_access"]
    
    for perm in permissions:
        scopes.append(f"https://graph.microsoft.com/{perm['permission']}")
    
    params = {
        "client_id": client_id,
        "response_type": "code",
        "redirect_uri": redirect_uri,
        "response_mode": "query",
        "scope": " ".join(scopes),
        "state": "user_consent",
        "prompt": "consent"  # Force consent screen
    }
    
    query_string = urllib.parse.urlencode(params)
    return f"{base_url}/{tenant_id}/oauth2/v2.0/authorize?{query_string}"

def generate_api_consent_url(tenant_id: str, frontend_client_id: str, api_client_id: str, redirect_uri: str = "http://localhost:3000") -> str:
    """Generate consent URL for API access."""
    base_url = "https://login.microsoftonline.com"
    
    params = {
        "client_id": frontend_client_id,
        "response_type": "code", 
        "redirect_uri": redirect_uri,
        "response_mode": "query",
        "scope": f"api://{api_client_id}/access_as_user openid profile",
        "state": "api_consent",
        "prompt": "consent"
    }
    
    query_string = urllib.parse.urlencode(params)
    return f"{base_url}/{tenant_id}/oauth2/v2.0/authorize?{query_string}"

def main():
    """Main function to generate and display consent URLs."""
    print("🔐 AI Chat Assistant - Consent URL Generator")
    print("=" * 50)
    print()
    
    # Load configuration
    env_vars = load_env_vars()
    
    tenant_id = env_vars.get('TENANT_ID')
    api_client_id = env_vars.get('API_CLIENT_ID') 
    frontend_client_id = env_vars.get('REACT_APP_CLIENT_ID')
    
    if not all([tenant_id, api_client_id, frontend_client_id]):
        print("❌ Error: Missing required configuration.")
        print("Please ensure these environment variables are set:")
        print("- TENANT_ID")
        print("- API_CLIENT_ID") 
        print("- REACT_APP_CLIENT_ID")
        return
    
    print(f"📋 Configuration:")
    print(f"   Tenant ID: {tenant_id}")
    print(f"   API Client ID: {api_client_id}")
    print(f"   Frontend Client ID: {frontend_client_id}")
    print()
    
    # Display required permissions
    print("📝 Required Microsoft Graph Permissions:")
    permissions = get_required_permissions()
    for perm in permissions:
        print(f"   • {perm['permission']} ({perm['type']}) - {perm['description']}")
    print()
    
    # Generate URLs
    print("🔗 Consent URLs:")
    print()
    
    # Admin consent URL (for tenant-wide approval)
    admin_url = generate_admin_consent_url(tenant_id, api_client_id)
    print("1️⃣ ADMIN CONSENT URL (Tenant-wide permissions):")
    print("   Use this if you want to grant permissions for all users in your tenant.")
    print(f"   {admin_url}")
    print()
    
    # User consent URL (for individual user approval)
    user_url = generate_user_consent_url(tenant_id, frontend_client_id)
    print("2️⃣ USER CONSENT URL (Individual user permissions):")
    print("   Use this for individual users to grant their own permissions.")
    print(f"   {user_url}")
    print()
    
    # API consent URL (for backend API access)
    api_url = generate_api_consent_url(tenant_id, frontend_client_id, api_client_id)
    print("3️⃣ API CONSENT URL (Backend API access):")
    print("   Use this to grant access to the backend API.")
    print(f"   {api_url}")
    print()
    
    print("📋 Instructions:")
    print("1. Choose the appropriate URL based on your needs:")
    print("   - Admin consent: For tenant-wide deployment")
    print("   - User consent: For individual user testing")
    print("   - API consent: For backend API access")
    print()
    print("2. Open the URL in a browser")
    print("3. Sign in with appropriate credentials")
    print("4. Review and approve the requested permissions")
    print("5. The application will then have the necessary permissions")
    print()
    print("⚠️  Note: After granting consent, users may still need to sign in")
    print("   to the application normally to use the AI chat features.")

if __name__ == "__main__":
    main()
