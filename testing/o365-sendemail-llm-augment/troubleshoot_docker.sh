#!/bin/bash

# Docker Compose Troubleshooting Script
# This script helps diagnose common Docker Compose issues

echo "🔍 Docker Compose Troubleshooting Script"
echo "========================================"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
print_status "Checking Docker status..."
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker Desktop."
    exit 1
else
    print_success "Docker is running"
fi

# Check if docker-compose is available
print_status "Checking docker-compose availability..."
if ! command -v docker-compose &> /dev/null; then
    print_error "docker-compose is not installed or not in PATH"
    exit 1
else
    print_success "docker-compose is available"
fi

# Check if we're in the right directory
print_status "Checking project structure..."
if [ ! -f "docker-compose.yml" ]; then
    print_error "docker-compose.yml not found. Please run this script from the project root."
    exit 1
fi

if [ ! -f ".env" ]; then
    print_error ".env file not found. Please copy .env.example to .env and configure it."
    exit 1
fi

if [ ! -f "frontend/.env" ]; then
    print_warning "frontend/.env file not found. This might cause frontend issues."
fi

print_success "Project structure looks good"

# Validate docker-compose.yml
print_status "Validating docker-compose.yml..."
if docker-compose config > /dev/null 2>&1; then
    print_success "docker-compose.yml is valid"
else
    print_error "docker-compose.yml has syntax errors:"
    docker-compose config
    exit 1
fi

# Check for port conflicts
print_status "Checking for port conflicts..."
if lsof -i :3000 > /dev/null 2>&1; then
    print_warning "Port 3000 is already in use:"
    lsof -i :3000
fi

if lsof -i :8000 > /dev/null 2>&1; then
    print_warning "Port 8000 is already in use:"
    lsof -i :8000
fi

# Check environment variables
print_status "Checking required environment variables..."
source .env

required_vars=("TENANT_ID" "API_CLIENT_ID" "API_CLIENT_SECRET")
missing_vars=()

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -eq 0 ]; then
    print_success "All required environment variables are set"
else
    print_error "Missing required environment variables: ${missing_vars[*]}"
fi

# Check Azure OpenAI variables
azure_vars=("AZURE_OPENAI_API_KEY" "AZURE_OPENAI_ENDPOINT")
missing_azure_vars=()

for var in "${azure_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_azure_vars+=("$var")
    fi
done

if [ ${#missing_azure_vars[@]} -eq 0 ]; then
    print_success "Azure OpenAI environment variables are set"
else
    print_warning "Missing Azure OpenAI variables: ${missing_azure_vars[*]} (chat functionality will not work)"
fi

# Check Docker resources
print_status "Checking Docker resources..."
docker system df

# Check for existing containers
print_status "Checking for existing containers..."
existing_containers=$(docker-compose ps -q)
if [ ! -z "$existing_containers" ]; then
    print_warning "Found existing containers. You might want to stop them first:"
    docker-compose ps
fi

echo ""
print_status "Troubleshooting complete!"
echo ""
echo "🚀 Recommended next steps:"
echo "1. If ports are in use: docker-compose down"
echo "2. Clean rebuild: docker-compose build --no-cache"
echo "3. Start services: docker-compose up"
echo ""
echo "For more verbose output, try: docker-compose up --build"
echo "To run in background: docker-compose up -d --build"
