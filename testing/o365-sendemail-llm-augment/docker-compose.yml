version: '3.8'

services:
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - TENANT_ID=${TENANT_ID}
      - API_CLIENT_ID=${API_CLIENT_ID}
      - API_CLIENT_SECRET=${API_CLIENT_SECRET}
      - FRONTEND_URL=${FRONTEND_URL:-http://localhost:3000}
      # Azure OpenAI Configuration
      - AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY}
      - AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT}
      - AZURE_OPENAI_DEPLOYMENT_NAME=${AZURE_OPENAI_DEPLOYMENT_NAME:-gpt-4}
      - AZURE_OPENAI_API_VERSION=${AZURE_OPENAI_API_VERSION:-2024-02-15-preview}
    env_file:
      - .env
    volumes:
      - ./backend:/app
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload

  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_CLIENT_ID=${REACT_APP_CLIENT_ID}
      - REACT_APP_TENANT_ID=${REACT_APP_TENANT_ID}
      - REACT_APP_API_CLIENT_ID=${REACT_APP_API_CLIENT_ID}
      - REACT_APP_REDIRECT_URI=${REACT_APP_REDIRECT_URI:-http://localhost:3000}
      - REACT_APP_API_BASE_URL=${REACT_APP_API_BASE_URL:-http://localhost:8000}
    volumes:
      - ./frontend:/app
      - /app/node_modules
    command: npm start
    depends_on:
      - backend
