# Troubleshooting Guide

## Common Error: AADSTS65001 - User or Administrator Has Not Consented

### Error Message
```
AADSTS65001: The user or administrator has not consented to use the application with ID 'xxx' named 'o365-email-api'. Send an interactive authorization request for this user and resource.
```

### What This Means
This error occurs when the user hasn't granted consent for the backend API to access Microsoft Graph on their behalf. This is expected behavior with delegated permissions.

### Solutions

#### Solution 1: Use the Updated Login Flow (Recommended)
The updated `LoginButton` component now automatically requests consent during login:

1. **Logout** if you're currently logged in
2. **Clear browser cache/cookies** for the application
3. **Login again** - you should see a consent screen asking for API permissions
4. **Accept the permissions** when prompted

#### Solution 2: Use the Consent Helper
If you're already logged in and encounter this error:

1. Try to send an email (you'll get the error)
2. A **"Grant API Permissions"** section will appear
3. Click **"Grant API Consent"** button
4. Accept the permissions in the popup
5. Try sending the email again

#### Solution 3: Manual Consent (Advanced)
If the above don't work, you can manually grant consent:

1. Go to Azure Portal > Azure Active Directory > App registrations
2. Find your frontend app registration
3. Go to "API permissions"
4. Click "Grant admin consent for [your organization]" (if you have admin rights)
5. OR ask each user to consent individually

### Verification Steps

1. **Check Azure App Registration Setup**:
   - Backend API app exposes `access_as_user` scope
   - Frontend app has permission to backend API scope
   - Both apps are in the same tenant

2. **Check Environment Variables**:
   ```bash
   # Frontend .env should have:
   REACT_APP_API_CLIENT_ID=your-backend-client-id  # This should match backend API app ID
   
   # Backend .env should have:
   API_CLIENT_ID=your-backend-client-id  # Same as above
   ```

3. **Check Browser Console**:
   - Look for MSAL errors during login
   - Verify token acquisition logs
   - Check if correct scopes are being requested

### Prevention

To avoid this error in the future:

1. **Use the updated login flow** that requests consent upfront
2. **Test with a fresh browser session** to simulate new user experience
3. **Document the consent process** for your users

## Other Common Issues

### CORS Errors
**Error**: `Access to XMLHttpRequest at 'http://localhost:8000/send-email' from origin 'http://localhost:3000' has been blocked by CORS policy`

**Solution**: 
- Check `FRONTEND_URL` in backend `.env` matches your frontend URL exactly
- Restart the backend after changing environment variables

### Invalid Client ID/Secret
**Error**: `AADSTS7000215: Invalid client secret is provided`

**Solution**:
- Verify `API_CLIENT_SECRET` in backend `.env` is correct
- Check if the secret has expired in Azure Portal
- Generate a new secret if needed

### Redirect URI Mismatch
**Error**: `AADSTS50011: The reply URL specified in the request does not match the reply URLs configured for the application`

**Solution**:
- Check redirect URI in frontend app registration matches exactly
- Verify protocol (http vs https)
- Check for trailing slashes

### Token Acquisition Fails
**Error**: Various MSAL token acquisition errors

**Solution**:
- Clear browser cache and cookies
- Try incognito/private browsing mode
- Check if user has proper licenses (Office 365, Exchange Online)

## Debug Mode

To enable debug logging:

### Frontend Debug
Add to browser console:
```javascript
localStorage.setItem('msal.log.level', 'Verbose');
localStorage.setItem('msal.log.pii', 'true');
```

### Backend Debug
Set environment variable:
```bash
export PYTHONPATH="${PYTHONPATH}:."
export MSAL_LOGGING_LEVEL=DEBUG
```

## Getting Help

If you're still experiencing issues:

1. **Check the browser console** for detailed error messages
2. **Check backend logs** for API errors
3. **Verify Azure app registration configuration** step by step
4. **Test with a different user account** to isolate user-specific issues
5. **Contact your Azure administrator** if you need tenant-level permissions

## Useful Links

- [Azure AD Error Codes](https://docs.microsoft.com/en-us/azure/active-directory/develop/reference-aadsts-error-codes)
- [MSAL.js Troubleshooting](https://docs.microsoft.com/en-us/azure/active-directory/develop/msal-js-troubleshooting)
- [Microsoft Graph API Documentation](https://docs.microsoft.com/en-us/graph/)
