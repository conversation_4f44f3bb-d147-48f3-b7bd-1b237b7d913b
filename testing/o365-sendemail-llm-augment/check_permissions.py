#!/usr/bin/env python3
"""
Check what permissions are currently configured and what's missing.
This helps diagnose permission-related issues.
"""

import os
import requests
import json
from typing import Dict, List

def load_env_vars():
    """Load environment variables from .env file."""
    env_vars = {}
    try:
        with open('.env', 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key] = value
    except FileNotFoundError:
        print("Warning: .env file not found. Using environment variables.")
    
    # Fallback to environment variables
    for key in ['TENANT_ID', 'API_CLIENT_ID', 'API_CLIENT_SECRET']:
        if key not in env_vars:
            env_vars[key] = os.environ.get(key, '')
    
    return env_vars

def get_required_permissions() -> List[str]:
    """Get the list of required Microsoft Graph permissions."""
    return [
        "Mail.Read",
        "Mail.ReadWrite", 
        "Mail.Send",
        "Calendars.Read",
        "Calendars.ReadWrite",
        "Contacts.Read",
        "Contacts.ReadWrite",
        "User.Read"
    ]

def check_app_permissions():
    """Check what permissions are configured for the app registration."""
    
    print("🔍 Permission Checker for AI Chat Assistant")
    print("=" * 50)
    print()
    
    env_vars = load_env_vars()
    tenant_id = env_vars.get('TENANT_ID')
    client_id = env_vars.get('API_CLIENT_ID')
    client_secret = env_vars.get('API_CLIENT_SECRET')
    
    if not all([tenant_id, client_id, client_secret]):
        print("❌ Error: Missing required configuration.")
        print("Please ensure these environment variables are set:")
        print("- TENANT_ID")
        print("- API_CLIENT_ID") 
        print("- API_CLIENT_SECRET")
        print()
        print("💡 You can check permissions manually in Azure Portal:")
        print(f"   https://portal.azure.com/#view/Microsoft_AAD_RegisteredApps/ApplicationMenuBlade/~/CallAnAPI/appId/{client_id}/isMSAApp~/false")
        return
    
    print(f"📋 Configuration:")
    print(f"   Tenant ID: {tenant_id}")
    print(f"   Client ID: {client_id}")
    print()
    
    # Try to get an access token to check permissions
    try:
        # Get access token using client credentials
        token_url = f"https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/token"
        token_data = {
            "grant_type": "client_credentials",
            "client_id": client_id,
            "client_secret": client_secret,
            "scope": "https://graph.microsoft.com/.default"
        }
        
        print("🔑 Attempting to get access token...")
        token_response = requests.post(token_url, data=token_data)
        
        if token_response.status_code == 200:
            print("✅ Successfully obtained access token")
            token_info = token_response.json()
            
            # Try to make a test call to Graph API
            headers = {
                "Authorization": f"Bearer {token_info['access_token']}",
                "Content-Type": "application/json"
            }
            
            # Test different endpoints
            test_endpoints = [
                ("User Profile", "https://graph.microsoft.com/v1.0/me"),
                ("Mail", "https://graph.microsoft.com/v1.0/me/messages?$top=1"),
                ("Calendar", "https://graph.microsoft.com/v1.0/me/events?$top=1"),
                ("Contacts", "https://graph.microsoft.com/v1.0/me/contacts?$top=1")
            ]
            
            print()
            print("🧪 Testing API endpoints:")
            for name, url in test_endpoints:
                try:
                    response = requests.get(url, headers=headers)
                    if response.status_code == 200:
                        print(f"   ✅ {name}: Working")
                    elif response.status_code == 403:
                        print(f"   ❌ {name}: 403 Forbidden (Missing permissions)")
                    else:
                        print(f"   ⚠️  {name}: {response.status_code} - {response.text[:100]}")
                except Exception as e:
                    print(f"   ❌ {name}: Error - {str(e)}")
            
        else:
            print(f"❌ Failed to get access token: {token_response.status_code}")
            print(f"   Response: {token_response.text}")
            
    except Exception as e:
        print(f"❌ Error checking permissions: {str(e)}")
    
    print()
    print("📝 Required Microsoft Graph Permissions:")
    required_perms = get_required_permissions()
    for perm in required_perms:
        print(f"   • {perm}")
    
    print()
    print("🔧 How to fix permission issues:")
    print("1. Go to Azure Portal > App registrations")
    print(f"2. Select your app: {client_id}")
    print("3. Go to API permissions")
    print("4. Add Microsoft Graph delegated permissions listed above")
    print("5. Grant admin consent for your organization")
    print()
    print("🔗 Direct link to your app permissions:")
    print(f"   https://portal.azure.com/#view/Microsoft_AAD_RegisteredApps/ApplicationMenuBlade/~/CallAnAPI/appId/{client_id}/isMSAApp~/false")
    print()
    print("📋 Or use the consent URLs:")
    print("   python simple_consent_urls.py")

if __name__ == "__main__":
    check_app_permissions()
