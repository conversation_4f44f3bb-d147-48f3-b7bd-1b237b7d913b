# Azure Entra ID Setup Guide

This guide will walk you through setting up Azure Entra ID app registrations for the O365 Email Augment application using **delegated permissions** (no admin consent required).

## Prerequisites

- Access to your company's Azure Entra ID tenant
- Permission to create app registrations in Entra ID
- Basic understanding of OAuth 2.0 and OpenID Connect

## Overview

You will need to create **two app registrations**:
1. **Backend API App Registration** - Exposes the API that the frontend will call
2. **Frontend SPA App Registration** - The React application that users will interact with

## Step 1: Create Backend API App Registration

### 1.1 Create the App Registration

1. Go to [Azure Portal](https://portal.azure.com)
2. Navigate to **Azure Active Directory** > **App registrations**
3. Click **New registration**
4. Fill in the details:
   - **Name**: `o365-email-api`
   - **Supported account types**: `Accounts in this organizational directory only (Single tenant)`
   - **Redirect URI**: Leave empty for now
5. Click **Register**

### 1.2 Note Important Values

After registration, note down these values (you'll need them later):
- **Application (client) ID** → This is your `2c0cbcbe-7d89-448d-a88e-02453f272ee3`
- **Directory (tenant) ID** → This is your `42cc3295-cd0e-449c-b98e-5ce5b560c1d3`

### 1.3 Create Client Secret

1. Go to **Certificates & secrets**
2. Click **New client secret**
3. Add description: `o365-email-augment-secret`
4. Choose expiration (recommend 24 months)
5. Click **Add**
6. **IMPORTANT**: Copy the secret value immediately → This is your `****************************************`

### 1.4 Expose an API

1. Go to **Expose an API**
2. Click **Set** next to "Application ID URI"
3. Accept the default URI (should be `api://2c0cbcbe-7d89-448d-a88e-02453f272ee3`)
4. Click **Save**
5. Click **Add a scope**
6. Fill in the scope details:
   - **Scope name**: `access_as_user`
   - **Who can consent**: `Admins and users`
   - **Admin consent display name**: `Access o365-email-augment-api as user`
   - **Admin consent description**: `Allow the application to access o365-email-augment-api on behalf of the signed-in user`
   - **User consent display name**: `Access email API as you`
   - **User consent description**: `Allow the application to send emails on your behalf`
   - **State**: `Enabled`
7. Click **Add scope**

### 1.5 Configure API Permissions

1. Go to **API permissions**
2. Click **Add a permission**
3. Select **Microsoft Graph**
4. Select **Delegated permissions**
5. Search for and select: `Mail.Send`
6. Click **Add permissions**

**Note**: Do NOT grant admin consent yet. We want to use delegated permissions where each user consents individually.

## Step 2: Create Frontend SPA App Registration

### 2.1 Create the App Registration

1. In **App registrations**, click **New registration**
2. Fill in the details:
   - **Name**: `o365-email-frontend`
   - **Supported account types**: `Accounts in this organizational directory only (Single tenant)`
   - **Redirect URI**: 
     - Type: `Single-page application (SPA)`
     - URI: `http://localhost:3000`
3. Click **Register**

### 2.2 Note Important Values

After registration, note down:
- **Application (client) ID** → This is your frontend `6261b1c1-29e2-4e6c-99c2-fb285c7de4d0`

### 2.3 Configure Authentication

1. Go to **Authentication**
2. Under **Single-page application**, ensure `http://localhost:3000` is listed
3. For production, add your production URL (e.g., `https://your-app.azurecontainerapps.io`)
4. Under **Advanced settings**:
   - **Allow public client flows**: `No`
   - **Live SDK support**: `No`
5. Click **Save**

### 2.4 Configure API Permissions

1. Go to **API permissions**
2. Click **Add a permission**
3. Select **My APIs**
4. Select your backend API (`o365-email-augment-api`)
5. Select **Delegated permissions**
6. Check `access_as_user`
7. Click **Add permissions**

**Note**: Again, do NOT grant admin consent. Users will consent individually.

## Step 3: Environment Configuration

### 3.1 Backend Environment Variables

Create/update `backend/.env`:

```env
TENANT_ID=your-tenant-id-from-step-1.2
API_CLIENT_ID=your-backend-client-id-from-step-1.2
API_CLIENT_SECRET=your-client-secret-from-step-1.3
FRONTEND_URL=http://localhost:3000
```

### 3.2 Frontend Environment Variables

Create/update `frontend/.env`:

```env
REACT_APP_CLIENT_ID=your-frontend-client-id-from-step-2.2
REACT_APP_TENANT_ID=your-tenant-id-from-step-1.2
REACT_APP_API_CLIENT_ID=your-backend-client-id-from-step-1.2
REACT_APP_REDIRECT_URI=http://localhost:3000
REACT_APP_API_BASE_URL=http://localhost:8000
```

## Step 4: Testing the Setup

### 4.1 Start the Applications

```bash
# Start backend
cd backend
python main.py

# Start frontend (in another terminal)
cd frontend
npm install
npm start
```

### 4.2 Test Authentication Flow

1. Open http://localhost:3000
2. Click "Sign in with Microsoft"
3. You should be redirected to your company's Entra ID login
4. After login, you may see a consent screen asking for permissions
5. Accept the permissions
6. You should be redirected back to the app and see the email form

### 4.3 Test Email Sending

1. Fill out the email form
2. Click "Send Email"
3. Check if the email was sent successfully

## Troubleshooting

### Common Issues

1. **"AADSTS50011: The reply URL specified in the request does not match the reply URLs configured for the application"**
   - Check that your redirect URI in the frontend app registration matches exactly
   - Ensure you're using the correct protocol (http vs https)

2. **"AADSTS65001: The user or administrator has not consented to use the application"**
   - This is expected for delegated permissions
   - Users need to consent when they first use the app
   - Make sure you didn't accidentally grant admin consent

3. **"AADSTS50105: The signed in user is not assigned to a role for the application"**
   - Check that the user has access to the application
   - Verify the app registration is configured for the correct tenant

4. **Backend OBO flow fails**
   - Verify the API permissions are correctly configured
   - Check that the backend API scope is properly exposed
   - Ensure the frontend is requesting the correct scope

### Verification Checklist

- [ ] Backend app registration created with correct permissions
- [ ] Frontend app registration created with SPA redirect URI
- [ ] API scope exposed on backend app registration
- [ ] Frontend app has permission to backend API scope
- [ ] Environment variables correctly configured
- [ ] Both applications start without errors
- [ ] Authentication flow works end-to-end
- [ ] Email sending works

## Security Considerations

1. **Client Secrets**: Store securely, never commit to version control
2. **Redirect URIs**: Only add trusted URIs
3. **Scopes**: Use minimum required permissions
4. **Token Storage**: Frontend uses sessionStorage for security
5. **HTTPS**: Always use HTTPS in production

## Production Deployment

When deploying to production:

1. Update redirect URIs in frontend app registration
2. Update CORS settings in backend
3. Use Azure Key Vault for secrets
4. Configure proper DNS and SSL certificates
5. Update environment variables with production URLs

## Additional Resources

- [Microsoft Graph API Documentation](https://docs.microsoft.com/en-us/graph/)
- [MSAL.js Documentation](https://docs.microsoft.com/en-us/azure/active-directory/develop/msal-overview)
- [Azure App Registration Guide](https://docs.microsoft.com/en-us/azure/active-directory/develop/quickstart-register-app)
