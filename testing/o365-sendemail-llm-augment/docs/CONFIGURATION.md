# Configuration Guide

This document provides detailed configuration instructions for the O365 Email Augment application.

## Environment Variables

### Backend Configuration

The backend requires the following environment variables in `backend/.env`:

| Variable | Description | Example |
|----------|-------------|---------|
| `TENANT_ID` | Your Azure AD tenant ID | `12345678-1234-1234-1234-123456789012` |
| `API_CLIENT_ID` | Backend app registration client ID | `*************-4321-4321-************` |
| `API_CLIENT_SECRET` | Backend app registration client secret | `your-secret-value-here` |
| `FRONTEND_URL` | Frontend application URL for CORS | `http://localhost:3000` |

### Frontend Configuration

The frontend requires the following environment variables in `frontend/.env`:

| Variable | Description | Example |
|----------|-------------|---------|
| `REACT_APP_CLIENT_ID` | Frontend app registration client ID | `11111111-**************-************` |
| `REACT_APP_TENANT_ID` | Your Azure AD tenant ID | `12345678-1234-1234-1234-123456789012` |
| `REACT_APP_API_CLIENT_ID` | Backend app registration client ID | `*************-4321-4321-************` |
| `REACT_APP_REDIRECT_URI` | Frontend redirect URI | `http://localhost:3000` |
| `REACT_APP_API_BASE_URL` | Backend API base URL | `http://localhost:8000` |

## MSAL Configuration

### Authentication Flow

The application uses the following MSAL configuration:

- **Authority**: `https://login.microsoftonline.com/{TENANT_ID}`
- **Client ID**: Frontend app registration ID
- **Redirect URI**: Frontend application URL
- **Scopes**: `api://{API_CLIENT_ID}/access_as_user`

### Token Caching

- **Cache Location**: `sessionStorage` (more secure than localStorage)
- **Store Auth State in Cookie**: `false` (not needed for modern browsers)

## API Permissions

### Required Permissions

The application requires the following permissions:

#### Backend API App Registration
- **Microsoft Graph**: `Mail.Send` (Delegated)

#### Frontend SPA App Registration
- **Custom API**: `api://{API_CLIENT_ID}/access_as_user` (Delegated)

### Permission Flow

1. User authenticates with frontend app
2. Frontend requests token with backend API scope
3. Backend uses On-Behalf-Of flow to get Graph API token
4. Backend calls Microsoft Graph API on user's behalf

## CORS Configuration

The backend is configured to allow requests from:
- The configured `FRONTEND_URL`
- `http://localhost:3000` (for development)

For production, update the CORS configuration to include your production frontend URL.

## Development vs Production

### Development Configuration

```env
# Backend
TENANT_ID=your-tenant-id
API_CLIENT_ID=your-backend-client-id
API_CLIENT_SECRET=your-backend-secret
FRONTEND_URL=http://localhost:3000

# Frontend
REACT_APP_CLIENT_ID=your-frontend-client-id
REACT_APP_TENANT_ID=your-tenant-id
REACT_APP_API_CLIENT_ID=your-backend-client-id
REACT_APP_REDIRECT_URI=http://localhost:3000
REACT_APP_API_BASE_URL=http://localhost:8000
```

### Production Configuration

```env
# Backend
TENANT_ID=your-tenant-id
API_CLIENT_ID=your-backend-client-id
API_CLIENT_SECRET=your-backend-secret
FRONTEND_URL=https://your-frontend.azurecontainerapps.io

# Frontend
REACT_APP_CLIENT_ID=your-frontend-client-id
REACT_APP_TENANT_ID=your-tenant-id
REACT_APP_API_CLIENT_ID=your-backend-client-id
REACT_APP_REDIRECT_URI=https://your-frontend.azurecontainerapps.io
REACT_APP_API_BASE_URL=https://your-backend.azurecontainerapps.io
```

## Security Best Practices

### Environment Variables
- Never commit `.env` files to version control
- Use Azure Key Vault for production secrets
- Rotate client secrets regularly

### Authentication
- Use HTTPS in production
- Configure proper redirect URIs
- Use minimum required scopes
- Implement proper error handling

### API Security
- Validate all incoming tokens
- Use CORS properly
- Log security events
- Monitor for unusual activity

## Troubleshooting

### Common Configuration Issues

1. **Invalid Client ID/Secret**
   - Verify values match Azure app registration
   - Check for extra spaces or characters
   - Ensure secret hasn't expired

2. **CORS Errors**
   - Check FRONTEND_URL matches exactly
   - Verify protocol (http vs https)
   - Ensure no trailing slashes

3. **Redirect URI Mismatch**
   - Check Azure app registration redirect URIs
   - Verify exact URL match including protocol
   - Check for case sensitivity

4. **Scope Issues**
   - Verify API scope is properly exposed
   - Check frontend is requesting correct scope
   - Ensure permissions are granted

### Validation Steps

1. Check all environment variables are set
2. Verify Azure app registrations are configured correctly
3. Test authentication flow
4. Verify API permissions
5. Test email sending functionality

## Monitoring and Logging

### Backend Logging
The backend logs important events:
- Authentication attempts
- Token acquisition
- Email sending attempts
- Errors and exceptions

### Frontend Logging
The frontend logs to browser console:
- Authentication events
- API calls
- Errors

For production, consider implementing proper logging infrastructure.
