<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Chat Assistant - Simple Permission Setup</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #7f8c8d;
            font-size: 1.1em;
        }
        .method {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #3498db;
        }
        .method h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 1em;
            font-weight: 600;
            text-decoration: none;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px 10px 10px 0;
        }
        .btn-admin {
            background: #e74c3c;
            color: white;
        }
        .btn-admin:hover {
            background: #c0392b;
            transform: translateY(-2px);
        }
        .btn-user {
            background: #3498db;
            color: white;
        }
        .btn-user:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        .btn-portal {
            background: #9b59b6;
            color: white;
        }
        .btn-portal:hover {
            background: #8e44ad;
            transform: translateY(-2px);
        }
        .info-box {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
        .success-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
        .steps {
            counter-reset: step-counter;
        }
        .step {
            counter-increment: step-counter;
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
            position: relative;
            padding-left: 40px;
        }
        .step::before {
            content: counter(step-counter);
            position: absolute;
            left: 10px;
            top: 10px;
            background: #3498db;
            color: white;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.8em;
        }
        .recommended {
            border-left-color: #27ae60;
        }
        .recommended h3::after {
            content: " ⭐ RECOMMENDED";
            color: #27ae60;
            font-size: 0.7em;
            font-weight: normal;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🤖</div>
            <h1>AI Chat Assistant</h1>
            <p class="subtitle">Simple Permission Setup (No PKCE Issues)</p>
        </div>

        <div class="info-box">
            <strong>🎯 What is this?</strong><br>
            This page provides simple ways to grant permissions for the AI Chat Assistant without encountering PKCE (Proof Key for Code Exchange) authentication issues.
        </div>

        <div class="method recommended">
            <h3>🏢 Admin Consent (Tenant-wide)</h3>
            <p><strong>Best for:</strong> Administrators who want to enable the app for all users</p>
            <p><strong>Effect:</strong> Grants permissions for ALL users in your organization</p>
            
            <div class="steps">
                <div class="step">Click the button below</div>
                <div class="step">Sign in with your admin account</div>
                <div class="step">Review and accept permissions</div>
                <div class="step">All users can now use the AI assistant</div>
            </div>
            
            <a href="#" id="adminConsentBtn" class="btn btn-admin">
                🏢 Grant Admin Consent
            </a>
        </div>

        <div class="method">
            <h3>👤 Individual User Consent</h3>
            <p><strong>Best for:</strong> Individual users who want to use the app</p>
            <p><strong>Effect:</strong> Grants permissions only for yourself</p>
            
            <div class="steps">
                <div class="step">Click the button below</div>
                <div class="step">Sign in with your work account</div>
                <div class="step">Review and accept permissions</div>
                <div class="step">You can now use the AI assistant</div>
            </div>
            
            <a href="#" id="userConsentBtn" class="btn btn-user">
                👤 Grant My Permissions
            </a>
        </div>

        <div class="method">
            <h3>🌐 Azure Portal Method</h3>
            <p><strong>Best for:</strong> Users who prefer the Azure Portal interface</p>
            <p><strong>Effect:</strong> Manual permission management through Azure Portal</p>
            
            <div class="steps">
                <div class="step">Click the button below</div>
                <div class="step">Navigate to API permissions</div>
                <div class="step">Manually grant required permissions</div>
                <div class="step">Return to the application</div>
            </div>
            
            <a href="#" id="portalBtn" class="btn btn-portal">
                🌐 Open Azure Portal
            </a>
        </div>

        <div class="success-box">
            <strong>✅ After granting permissions:</strong>
            <ul>
                <li>Return to the AI Chat Assistant application</li>
                <li>Sign in normally with your work account</li>
                <li>Start using the AI chat features immediately</li>
                <li>Try commands like "Show me my recent emails" or "What meetings do I have today?"</li>
            </ul>
        </div>

        <div class="info-box">
            <strong>🔒 Security Notes:</strong>
            <ul>
                <li>These permissions allow the AI to access your Outlook 365 data</li>
                <li>The AI only accesses data when you explicitly request it</li>
                <li>No data is stored permanently by the application</li>
                <li>You can revoke permissions at any time in your Microsoft account settings</li>
            </ul>
        </div>

        <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
        
        <p style="text-align: center; color: #7f8c8d; font-size: 0.9em;">
            Having issues? Contact your IT administrator or try the alternative methods above.
        </p>
    </div>

    <script>
        // Configuration
        const TENANT_ID = '42cc3295-cd0e-449c-b98e-5ce5b560c1d3';
        const API_CLIENT_ID = '2c0cbcbe-7d89-448d-a88e-02453f272ee3';
        const FRONTEND_CLIENT_ID = '6261b1c1-29e2-4e6c-99c2-fb285c7de4d0';

        // Generate simple consent URLs (no PKCE)
        const adminConsentUrl = `https://login.microsoftonline.com/${TENANT_ID}/v2.0/adminconsent?client_id=${API_CLIENT_ID}&scope=${encodeURIComponent('https://graph.microsoft.com/.default')}&redirect_uri=${encodeURIComponent('https://localhost')}`;

        const userConsentUrl = `https://login.microsoftonline.com/${TENANT_ID}/oauth2/v2.0/authorize?client_id=${FRONTEND_CLIENT_ID}&response_type=token&redirect_uri=${encodeURIComponent('http://localhost:3000')}&scope=${encodeURIComponent('https://graph.microsoft.com/Mail.Read https://graph.microsoft.com/Mail.ReadWrite https://graph.microsoft.com/Mail.Send https://graph.microsoft.com/Calendars.Read https://graph.microsoft.com/Calendars.ReadWrite https://graph.microsoft.com/Contacts.Read https://graph.microsoft.com/Contacts.ReadWrite https://graph.microsoft.com/User.Read')}&response_mode=fragment&state=consent_granted&nonce=consent_nonce&prompt=consent`;

        const portalUrl = `https://portal.azure.com/#view/Microsoft_AAD_RegisteredApps/ApplicationMenuBlade/~/CallAnAPI/appId/${API_CLIENT_ID}/isMSAApp~/false`;

        // Set button URLs
        document.getElementById('adminConsentBtn').href = adminConsentUrl;
        document.getElementById('userConsentBtn').href = userConsentUrl;
        document.getElementById('portalBtn').href = portalUrl;

        // Add click tracking
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                const type = this.id.replace('Btn', '');
                console.log(`Consent button clicked: ${type}`);
                
                // Show a helpful message
                if (type === 'adminConsent') {
                    alert('You will be redirected to grant admin consent for all users in your tenant. Only administrators can complete this action.');
                } else if (type === 'userConsent') {
                    alert('You will be redirected to grant permissions for yourself. After completing this, you can use the AI chat features.');
                } else if (type === 'portal') {
                    alert('You will be redirected to the Azure Portal where you can manually manage permissions.');
                }
            });
        });
    </script>
</body>
</html>
