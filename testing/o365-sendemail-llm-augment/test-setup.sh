#!/bin/bash

echo "🧪 Testing O365 Email Augment Setup"
echo "===================================="

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ Error: .env file not found!"
    echo "Please copy .env.example to .env and fill in your Azure values."
    exit 1
fi

# Load environment variables
source .env

# Check required environment variables
echo "📋 Checking environment variables..."

required_vars=("TENANT_ID" "API_CLIENT_ID" "API_CLIENT_SECRET" "REACT_APP_CLIENT_ID" "REACT_APP_TENANT_ID" "REACT_APP_API_CLIENT_ID")

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "❌ Missing: $var"
        exit 1
    else
        echo "✅ Found: $var"
    fi
done

echo ""
echo "🔍 Configuration Summary:"
echo "Tenant ID: $TENANT_ID"
echo "Backend API Client ID: $API_CLIENT_ID"
echo "Frontend Client ID: $REACT_APP_CLIENT_ID"
echo ""

# Test backend health
echo "🏥 Testing backend health..."
if curl -s http://localhost:8000/health > /dev/null; then
    echo "✅ Backend is running"
    curl -s http://localhost:8000/health | python3 -m json.tool
else
    echo "❌ Backend is not responding"
    echo "Make sure to start the backend first: docker-compose up backend"
fi

echo ""
echo "🌐 Testing frontend..."
if curl -s http://localhost:3000 > /dev/null; then
    echo "✅ Frontend is running"
else
    echo "❌ Frontend is not responding"
    echo "Make sure to start the frontend: docker-compose up frontend"
fi

echo ""
echo "📚 Next steps:"
echo "1. Open http://localhost:3000 in your browser"
echo "2. Click 'Sign in with Microsoft'"
echo "3. When sending an email, you may see a consent popup - accept it"
echo "4. If you get consent errors, use the 'Grant API Consent' button"
echo ""
echo "📖 For detailed troubleshooting, see TROUBLESHOOTING.md"
