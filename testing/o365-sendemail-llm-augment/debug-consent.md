# Debug Consent Issue - Step by Step

## Current Error Analysis

The error `AADSTS65001` with application ID `2c0cbcbe-7d89-448d-a88e-02453f272ee3` means:

1. **The user authenticated successfully** (frontend login worked)
2. **The backend received a token** (OBO flow started)
3. **Azure rejected the OBO request** because the user hasn't consented to the backend API

## Diagnostic Steps

### Step 1: Verify Your App Registration IDs

Check your environment variables match your Azure app registrations:

**Backend App Registration ID**: `2c0cbcbe-7d89-448d-a88e-02453f272ee3`
- This should be your `API_CLIENT_ID` in backend/.env
- This should be your `REACT_APP_API_CLIENT_ID` in frontend/.env

**Frontend App Registration ID**: Should be different
- This should be your `REACT_APP_CLIENT_ID` in frontend/.env

### Step 2: Check Azure App Registration Configuration

#### Backend API App Registration (`2c0cbcbe-7d89-448d-a88e-02453f272ee3`)

1. Go to Azure Portal > App registrations > Find this app
2. **Expose an API** tab:
   - Should have Application ID URI: `api://2c0cbcbe-7d89-448d-a88e-02453f272ee3`
   - Should have scope: `access_as_user`
3. **API permissions** tab:
   - Should have `Microsoft Graph > Mail.Send (Delegated)`
   - Status should show "Granted" or "Not granted" (this is OK for delegated)

#### Frontend SPA App Registration (different ID)

1. **API permissions** tab:
   - Should have `My APIs > o365-email-api > access_as_user (Delegated)`
   - Status: "Not granted" is OK for delegated permissions

### Step 3: Common Configuration Issues

#### Issue A: Wrong Application ID URI
The backend API might have wrong Application ID URI.

**Fix**: 
1. Go to backend app > Expose an API
2. Set Application ID URI to: `api://2c0cbcbe-7d89-448d-a88e-02453f272ee3`

#### Issue B: Frontend Not Requesting Correct Scope
The frontend might not be requesting the right scope.

**Check**: Frontend should request scope: `api://2c0cbcbe-7d89-448d-a88e-02453f272ee3/access_as_user`

#### Issue C: Missing Preauthorized Applications
The backend API might need to preauthorize the frontend.

**Fix**:
1. Go to backend app > Expose an API
2. Add authorized client application
3. Client ID: Your frontend app registration ID
4. Authorized scopes: Check `access_as_user`

## Quick Fix Solutions

### Solution 1: Admin Consent (If You Have Admin Rights)

1. Go to frontend app registration > API permissions
2. Click "Grant admin consent for [your organization]"
3. This will consent for all users in your tenant

### Solution 2: Force User Consent

Add this to your frontend token request:
```javascript
const request = {
  scopes: [`api://2c0cbcbe-7d89-448d-a88e-02453f272ee3/access_as_user`],
  account: accounts[0],
  prompt: 'consent'
};
```

### Solution 3: Preauthorize Frontend App

1. Go to backend app registration
2. Expose an API > Authorized client applications
3. Add your frontend client ID
4. Select the `access_as_user` scope

## Testing Commands

### Check Token Contents
Add this to your frontend console to see what scopes are in your token:

```javascript
// In browser console after login
const accounts = window.msal.getAllAccounts();
console.log('Accounts:', accounts);

// Check what scopes were granted
const tokenRequest = {
  scopes: ['api://2c0cbcbe-7d89-448d-a88e-02453f272ee3/access_as_user'],
  account: accounts[0]
};

window.msal.acquireTokenSilent(tokenRequest)
  .then(response => {
    console.log('Token response:', response);
    console.log('Scopes granted:', response.scopes);
  })
  .catch(error => {
    console.log('Token error:', error);
  });
```

### Check Backend Token
Add this to your backend to see what's in the incoming token:

```python
import jwt
import base64

# In your backend endpoint, before OBO flow:
def decode_token_payload(token):
    # Split the token and decode the payload (without verification for debugging)
    parts = token.split('.')
    payload = parts[1]
    # Add padding if needed
    payload += '=' * (4 - len(payload) % 4)
    decoded = base64.urlsafe_b64decode(payload)
    return json.loads(decoded)

# Add this in your send_email endpoint:
token_payload = decode_token_payload(user_token)
logger.info(f"Token audience: {token_payload.get('aud')}")
logger.info(f"Token scopes: {token_payload.get('scp')}")
```

## Next Steps

1. **Verify your app registration IDs match the error message**
2. **Check the Application ID URI format**
3. **Try Solution 3 (preauthorize) first** - it's the most reliable
4. **If you have admin rights, try Solution 1**
5. **Use the testing commands to debug token contents**

The most common cause is that the frontend app isn't properly authorized to request tokens for the backend API scope.
