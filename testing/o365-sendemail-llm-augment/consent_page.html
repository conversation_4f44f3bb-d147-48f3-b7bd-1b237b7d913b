<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Chat Assistant - Grant Permissions</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #7f8c8d;
            font-size: 1.1em;
        }
        .permissions-list {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .permission-item {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 8px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        .permission-icon {
            margin-right: 10px;
            font-size: 1.2em;
        }
        .consent-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 1em;
            font-weight: 600;
            text-decoration: none;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 200px;
        }
        .btn-primary {
            background: #3498db;
            color: white;
        }
        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        .btn-secondary {
            background: #95a5a6;
            color: white;
        }
        .btn-secondary:hover {
            background: #7f8c8d;
            transform: translateY(-2px);
        }
        .btn-admin {
            background: #e74c3c;
            color: white;
        }
        .btn-admin:hover {
            background: #c0392b;
            transform: translateY(-2px);
        }
        .info-box {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
        .steps {
            counter-reset: step-counter;
        }
        .step {
            counter-increment: step-counter;
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            position: relative;
            padding-left: 50px;
        }
        .step::before {
            content: counter(step-counter);
            position: absolute;
            left: 15px;
            top: 15px;
            background: #3498db;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.9em;
        }
        @media (max-width: 600px) {
            .consent-buttons {
                flex-direction: column;
                align-items: center;
            }
            .btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🤖</div>
            <h1>AI Chat Assistant</h1>
            <p class="subtitle">Grant permissions to use Outlook 365 features</p>
        </div>

        <div class="info-box">
            <strong>🎯 What is this?</strong><br>
            The AI Chat Assistant needs permission to access your Outlook 365 data to help you manage emails, calendar events, and contacts through natural language commands.
        </div>

        <h3>📋 Required Permissions</h3>
        <div class="permissions-list">
            <div class="permission-item">
                <span class="permission-icon">📧</span>
                <div>
                    <strong>Email Access</strong><br>
                    <small>Read, write, and send emails on your behalf</small>
                </div>
            </div>
            <div class="permission-item">
                <span class="permission-icon">📅</span>
                <div>
                    <strong>Calendar Access</strong><br>
                    <small>Read and manage your calendar events</small>
                </div>
            </div>
            <div class="permission-item">
                <span class="permission-icon">👥</span>
                <div>
                    <strong>Contacts Access</strong><br>
                    <small>Read and manage your contacts</small>
                </div>
            </div>
            <div class="permission-item">
                <span class="permission-icon">👤</span>
                <div>
                    <strong>Profile Access</strong><br>
                    <small>Read your basic profile information</small>
                </div>
            </div>
        </div>

        <h3>🚀 Grant Permissions</h3>
        <p>Choose the appropriate option based on your role:</p>

        <div class="consent-buttons">
            <a href="#" id="userConsentBtn" class="btn btn-primary">
                👤 Grant My Permissions
            </a>
            <a href="#" id="apiConsentBtn" class="btn btn-secondary">
                🔌 Grant API Access
            </a>
            <a href="#" id="adminConsentBtn" class="btn btn-admin">
                🏢 Admin: Grant for All Users
            </a>
        </div>

        <div class="warning-box">
            <strong>⚠️ Important:</strong> 
            <ul>
                <li>These permissions allow the AI to act on your behalf</li>
                <li>You can revoke permissions at any time in your Microsoft account settings</li>
                <li>The application only accesses data when you explicitly request it through chat</li>
                <li>No data is stored permanently by the application</li>
            </ul>
        </div>

        <h3>📝 How it works</h3>
        <div class="steps">
            <div class="step">
                Click one of the permission buttons above
            </div>
            <div class="step">
                Sign in with your work Microsoft account
            </div>
            <div class="step">
                Review the permissions and click "Accept"
            </div>
            <div class="step">
                Return to the AI Chat Assistant application
            </div>
            <div class="step">
                Start chatting with your AI assistant!
            </div>
        </div>

        <div class="info-box">
            <strong>💬 Example commands you can try:</strong><br>
            • "Show me my recent emails"<br>
            • "What meetings do I have today?"<br>
            • "Send an email to John about the project update"<br>
            • "Create a meeting for tomorrow at 2 PM"<br>
            • "Find contacts with 'Smith' in the name"
        </div>

        <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
        
        <p style="text-align: center; color: #7f8c8d; font-size: 0.9em;">
            Need help? Contact your IT administrator or check the application documentation.
        </p>
    </div>

    <script>
        // Configuration - Update these URLs with your actual consent URLs
        const TENANT_ID = '42cc3295-cd0e-449c-b98e-5ce5b560c1d3';
        const API_CLIENT_ID = '2c0cbcbe-7d89-448d-a88e-02453f272ee3';
        const FRONTEND_CLIENT_ID = '6261b1c1-29e2-4e6c-99c2-fb285c7de4d0';
        const REDIRECT_URI = 'http://localhost:3000';

        // Generate consent URLs
        const adminConsentUrl = `https://login.microsoftonline.com/${TENANT_ID}/v2.0/adminconsent?client_id=${API_CLIENT_ID}&response_type=code&redirect_uri=https://localhost&response_mode=query&scope=https://graph.microsoft.com/.default&state=admin_consent`;

        const userConsentUrl = `https://login.microsoftonline.com/${TENANT_ID}/oauth2/v2.0/authorize?client_id=${FRONTEND_CLIENT_ID}&response_type=code&redirect_uri=${encodeURIComponent(REDIRECT_URI)}&response_mode=query&scope=${encodeURIComponent('openid profile offline_access https://graph.microsoft.com/Mail.Read https://graph.microsoft.com/Mail.ReadWrite https://graph.microsoft.com/Mail.Send https://graph.microsoft.com/Calendars.Read https://graph.microsoft.com/Calendars.ReadWrite https://graph.microsoft.com/Contacts.Read https://graph.microsoft.com/Contacts.ReadWrite https://graph.microsoft.com/User.Read')}&state=user_consent&prompt=consent`;

        const apiConsentUrl = `https://login.microsoftonline.com/${TENANT_ID}/oauth2/v2.0/authorize?client_id=${FRONTEND_CLIENT_ID}&response_type=code&redirect_uri=${encodeURIComponent(REDIRECT_URI)}&response_mode=query&scope=${encodeURIComponent(`api://${API_CLIENT_ID}/access_as_user openid profile`)}&state=api_consent&prompt=consent`;

        // Set button URLs
        document.getElementById('userConsentBtn').href = userConsentUrl;
        document.getElementById('apiConsentBtn').href = apiConsentUrl;
        document.getElementById('adminConsentBtn').href = adminConsentUrl;

        // Add click tracking
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                const type = this.id.replace('ConsentBtn', '');
                console.log(`Consent button clicked: ${type}`);
                
                // Optional: Add analytics tracking here
                // gtag('event', 'consent_click', { consent_type: type });
            });
        });
    </script>
</body>
</html>
